{"name": "@scarf/scarf", "version": "1.4.0", "description": "Scarf is like Google Analytics for your npm packages. Gain insights into how your packages are installed and used, and by which companies.", "main": "report.js", "homepage": "https://github.com/scarf-sh/scarf-js", "repository": {"type": "git", "url": "git+https://github.com/scarf-sh/scarf-js.git"}, "files": ["report.js"], "scripts": {"postinstall": "node ./report.js", "test": "jest --verbose"}, "author": "Scarf Systems", "license": "Apache-2.0", "devDependencies": {"jest": "^25.3.0", "minimist": "^1.2.2", "standard": "^14.3.1"}, "standard": {"globals": ["expect", "test", "jest", "beforeAll", "afterAll", "fail", "describe"]}}