import http from 'k6/http';
import { group, check } from 'k6';
import {
  ADMIN_BASE,
  ADMIN_AUTH_TOKEN,
  EXCHANGE,
  USER_ID,         // IU-070P89
  OVERSEE_USER_ID  // AG-OKIDA6
} from '../config/config.js';

const validHeaders = {
  'Content-Type': 'application/json',
  Authorization: ADMIN_AUTH_TOKEN,
};

const badHeaders = {
  'Content-Type': 'application/json',
  Authorization: 'Bearer invalid-token',
};

const noAuthHeaders = {
  'Content-Type': 'application/json',
};

export const options = {
  vus: 1,
  iterations: 1,
};

export default function () {
  goodUserLimitsAndLeverage();
  badUserLimitsAndLeverage();
}

export function goodUserLimitsAndLeverage() {
  group('✅ userLimitsAndLeverage Good Flows', () => {
    // 1. User Status
    const statusRes = http.get(`${ADMIN_BASE}/user/status/${OVERSEE_USER_ID}`, { headers: validHeaders });

    const statusOk = check(statusRes, {
      'userLimitsAndLeverage GET /user/status → 200': (r) => r.status === 200,
      'userLimitsAndLeverage GET /user/status → is_enabled present': (r) => {
        try {
          const json = r.json();
          return Array.isArray(json.is_enabled) || typeof json.is_enabled === 'boolean';
        } catch (e) {
          console.error('userLimitsAndLeverage GET /user/status → JSON parse error:', e.message);
          return false;
        }
      },
    });

    if (!statusOk) {
      console.error('userLimitsAndLeverage GET /user/status failed:', statusRes.status, statusRes.body);
    }

    // 2. User Config
    const userCfg = http.get(`${ADMIN_BASE}/user-config/${USER_ID}`, { headers: validHeaders });

    const userCfgOk = check(userCfg, {
      'userLimitsAndLeverage GET /user-config → 200': (r) => r.status === 200,
      [`userLimitsAndLeverage GET /user-config → has ${EXCHANGE}`]: (r) => {
        try {
          const json = r.json();
          return json.config?.[EXCHANGE] !== undefined;
        } catch (e) {
          console.error('userLimitsAndLeverage GET /user-config → JSON parse error:', e.message);
          return false;
        }
      },
    });

    if (!userCfgOk) {
      console.error('userLimitsAndLeverage GET /user-config failed:', userCfg.status, userCfg.body);
    }

    // 3. Leverage Config
    const levCfg = http.get(`${ADMIN_BASE}/user-leverageconfig/${USER_ID}`, { headers: validHeaders });

    const levCfgOk = check(levCfg, {
      'userLimitsAndLeverage GET /user-leverageconfig → 200': (r) => r.status === 200,
      [`userLimitsAndLeverage GET /user-leverageconfig → ${EXCHANGE} exists`]: (r) => {
        try {
          const json = r.json();
          return json.config?.[EXCHANGE] !== undefined;
        } catch (e) {
          console.error('userLimitsAndLeverage GET /user-leverageconfig → JSON parse error:', e.message);
          return false;
        }
      },
    });

    if (!levCfgOk) {
      console.error('userLimitsAndLeverage GET /user-leverageconfig failed:', levCfg.status, levCfg.body);
    }

    // 4. Exchange Enablement
    const exCfg = http.get(`${ADMIN_BASE}/user/exchanges/${USER_ID}`, { headers: validHeaders });

    const exCfgOk = check(exCfg, {
      'userLimitsAndLeverage GET /user/exchanges → 200': (r) => r.status === 200,
      [`userLimitsAndLeverage GET /user/exchanges → ${EXCHANGE} enabled`]: (r) => {
        try {
          const json = r.json();
          return json[EXCHANGE] === true || json[EXCHANGE] === false; // Check if property exists
        } catch (e) {
          console.error('userLimitsAndLeverage GET /user/exchanges → JSON parse error:', e.message);
          return false;
        }
      },
    });

    if (!exCfgOk) {
      console.error('userLimitsAndLeverage GET /user/exchanges failed:', exCfg.status, exCfg.body);
    }
  });
}

export function badUserLimitsAndLeverage() {
  group('❌ userLimitsAndLeverage Bad Flows', () => {
    const BAD_USER = 'INVALID-USER-ID';

    // Test helper function
    const test = (desc, url, headers, expectedStatus = 400) => {
      const res = http.get(url, { headers });
      const ok = check(res, {
        [`${desc} → ${expectedStatus}+`]: (r) => r.status >= expectedStatus,
      });
      if (!ok) {
        console.error(`${desc} failed →`, res.status, res.body);
      }
    };

    // Test helper for endpoints that return 200 with empty/default data for invalid users
    const testInvalidUser = (desc, url, headers) => {
      const res = http.get(url, { headers });
      const ok = check(res, {
        [`${desc} → 200 or 400+`]: (r) => r.status === 200 || r.status >= 400,
      });
      if (!ok) {
        console.error(`${desc} failed →`, res.status, res.body);
      }
    };

    // Test invalid user IDs
    testInvalidUser(
      'userLimitsAndLeverage GET /user/status → invalid user_id',
      `${ADMIN_BASE}/user/status/${BAD_USER}`,
      validHeaders
    );

    testInvalidUser(
      'userLimitsAndLeverage GET /user-config → invalid user_id',
      `${ADMIN_BASE}/user-config/${BAD_USER}`,
      validHeaders
    );

    testInvalidUser(
      'userLimitsAndLeverage GET /user-leverageconfig → invalid user_id',
      `${ADMIN_BASE}/user-leverageconfig/${BAD_USER}`,
      validHeaders
    );

    testInvalidUser(
      'userLimitsAndLeverage GET /user/exchanges → invalid user_id',
      `${ADMIN_BASE}/user/exchanges/${BAD_USER}`,
      validHeaders
    );

    // Test missing authentication
    test(
      'userLimitsAndLeverage GET /user/status → no auth',
      `${ADMIN_BASE}/user/status/${OVERSEE_USER_ID}`,
      noAuthHeaders,
      401
    );

    test(
      'userLimitsAndLeverage GET /user-config → no auth',
      `${ADMIN_BASE}/user-config/${USER_ID}`,
      noAuthHeaders,
      401
    );

    test(
      'userLimitsAndLeverage GET /user-leverageconfig → no auth',
      `${ADMIN_BASE}/user-leverageconfig/${USER_ID}`,
      noAuthHeaders,
      401
    );

    test(
      'userLimitsAndLeverage GET /user/exchanges → no auth',
      `${ADMIN_BASE}/user/exchanges/${USER_ID}`,
      noAuthHeaders,
      401
    );

    // Test invalid authentication
    test(
      'userLimitsAndLeverage GET /user/status → bad token',
      `${ADMIN_BASE}/user/status/${OVERSEE_USER_ID}`,
      badHeaders,
      401
    );

    test(
      'userLimitsAndLeverage GET /user-config → bad token',
      `${ADMIN_BASE}/user-config/${USER_ID}`,
      badHeaders,
      401
    );

    test(
      'userLimitsAndLeverage GET /user-leverageconfig → bad token',
      `${ADMIN_BASE}/user-leverageconfig/${USER_ID}`,
      badHeaders,
      401
    );

    test(
      'userLimitsAndLeverage GET /user/exchanges → bad token',
      `${ADMIN_BASE}/user/exchanges/${USER_ID}`,
      badHeaders,
      401
    );

    // Test malformed requests (non-existent endpoints)
    test(
      'userLimitsAndLeverage GET /user/invalid-endpoint → 404',
      `${ADMIN_BASE}/user/invalid-endpoint/${USER_ID}`,
      validHeaders,
      404
    );
  });
}
