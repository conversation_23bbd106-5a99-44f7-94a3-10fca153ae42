# extract-tokens.ps1
# PowerShell script to extract user tokens from K6 login log file

param(
    [string]$logPath = "login_output.log",
    [string]$outputCsv = "data/users_with_tokens.csv"
)

Write-Host "Starting token extraction from log file..." -ForegroundColor Green
Write-Host "Log file: $logPath" -ForegroundColor Cyan
Write-Host "Output CSV: $outputCsv" -ForegroundColor Cyan

# Check if log file exists
if (-not (Test-Path $logPath)) {
    Write-Host "ERROR: Log file '$logPath' not found!" -ForegroundColor Red
    Write-Host "Make sure to run the K6 login script first:" -ForegroundColor Yellow
    Write-Host "   k6 run scripts/loginWithCsv.js > login_output.log 2>&1" -ForegroundColor Yellow
    exit 1
}

try {
    # Read ENTIRE file as one string (-Raw) and try UTF-8 first
    Write-Host "Reading log file..." -ForegroundColor Yellow
    $text = Get-Content -Path $logPath -Raw -Encoding UTF8

    # Find every msg=" … " blob. The 'Singleline' option lets '.' match new-lines.
    Write-Host "Extracting token data..." -ForegroundColor Yellow
    $regex = 'level=info msg="([^"]+)"\s+source=console'
    $matches = [regex]::Matches($text, $regex, 'Singleline')

    Write-Host "Found $($matches.Count) console messages" -ForegroundColor Cyan

    # Build the CSV rows
    $rows = foreach ($m in $matches) {
        $payload = $m.Groups[1].Value -replace '[\r\n]'  # flatten embedded CR/LF

        # Skip header copies and non-data lines
        if ($payload -eq 'user_id,password,token') { continue }
        if ($payload -eq 'COPY BELOW CONTENT TO: /data/users_with_tokens.csv') { continue }
        if ($payload -eq '') { continue }
        if ($payload -notmatch '^IU-') { continue }  # keep only real data starting with IU-

        $payload
    }

    Write-Host "Extracted $($rows.Count) user records" -ForegroundColor Green

    if ($rows.Count -eq 0) {
        Write-Host "WARNING: No user records found in log file!" -ForegroundColor Yellow
        Write-Host "Check if the K6 script ran successfully and generated token data" -ForegroundColor Yellow
        exit 1
    }

    # Ensure output directory exists
    $outputDir = Split-Path $outputCsv -Parent
    if ($outputDir -and -not (Test-Path $outputDir)) {
        Write-Host "Creating output directory: $outputDir" -ForegroundColor Yellow
        New-Item -ItemType Directory -Path $outputDir -Force | Out-Null
    }

    # Inject header and write out
    Write-Host "Writing CSV file..." -ForegroundColor Yellow
    @('user_id,password,token') + $rows | Out-File -FilePath $outputCsv -Encoding UTF8 -Force

    Write-Host ""
    Write-Host "SUCCESS!" -ForegroundColor Green
    Write-Host "$($rows.Count) user records written to $outputCsv" -ForegroundColor Green
    Write-Host ""

    # Display summary of extracted data
    Write-Host "Summary:" -ForegroundColor Cyan
    $tokensWithData = ($rows | Where-Object { $_.Split(',')[2] -ne '' }).Count
    $tokensEmpty = $rows.Count - $tokensWithData
    
    Write-Host "   Total users: $($rows.Count)" -ForegroundColor White
    Write-Host "   With tokens: $tokensWithData" -ForegroundColor Green
    Write-Host "   Failed logins: $tokensEmpty" -ForegroundColor Red
    
    if ($tokensWithData -gt 0) {
        $successRate = [math]::Round(($tokensWithData / $rows.Count) * 100, 1)
        Write-Host "   Success rate: $successRate%" -ForegroundColor Cyan
    }

    Write-Host ""
    Write-Host "Token extraction completed successfully!" -ForegroundColor Green

} catch {
    Write-Host ""
    Write-Host "ERROR during token extraction:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    Write-Host ""
    Write-Host "Troubleshooting tips:" -ForegroundColor Yellow
    Write-Host "   1. Check if the log file is properly formatted" -ForegroundColor Yellow
    Write-Host "   2. Ensure the K6 script completed successfully" -ForegroundColor Yellow
    Write-Host "   3. Verify file permissions for the output directory" -ForegroundColor Yellow
    exit 1
}
