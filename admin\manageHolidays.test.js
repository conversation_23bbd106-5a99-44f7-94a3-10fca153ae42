import http from 'k6/http';
import { group, check } from 'k6';
import {
  ADMIN_BASE,
  ADMIN_AUTH_TOKEN,
  MARKET_TIMINGS
} from '../config/config.js';

const headers = {
  'Content-Type': 'application/json',
  Authorization: ADMIN_AUTH_TOKEN
};

const noAuthHeaders = {
  'Content-Type': 'application/json'
};

const badHeaders = {
  'Content-Type': 'application/json',
  Authorization: 'Bearer invalid-token'
};

export const options = {
  vus: 1,
  iterations: 1,
};

export default function () {
  goodManageHolidays();
  badManageHolidays();
}

// Legacy function names for backward compatibility
export const testHolidayFullAndPartialFlow = goodManageHolidays;
export const testHolidayNegativeCases = badManageHolidays;

export function goodManageHolidays() {
  group('✅ Manage Holidays - Good Flow', () => {
    const today = new Date();
    const dateStr = today.toISOString().split('T')[0];
    const nextDay = new Date(today.getTime() + 86400000).toISOString().split('T')[0];

    // Get all available markets from config
    const markets = Object.keys(MARKET_TIMINGS);
    console.log(`🏢 Testing holidays for all markets: ${markets.join(', ')}`);

    // Store created holiday IDs for cleanup
    const createdHolidays = [];

    // Step 1: Get initial holiday counts for all markets
    const initialCountRes = http.get(`${ADMIN_BASE}/holidays/count-by-market/`, { headers });
    const initialCountOk = check(initialCountRes, {
      'manageHolidays GET /holidays/count-by-market → 200': (r) => r.status === 200,
      'manageHolidays GET /holidays/count-by-market → valid response': (r) => {
        try {
          const json = r.json();
          return Array.isArray(json);
        } catch (e) {
          console.error('manageHolidays GET /holidays/count-by-market → JSON parse error:', e.message);
          return false;
        }
      }
    });

    if (!initialCountOk) {
      console.error('manageHolidays GET /holidays/count-by-market failed:', initialCountRes.status, initialCountRes.body);
      return;
    }

    // Store initial counts for all markets
    const initialCounts = {};
    try {
      const countData = initialCountRes.json();
      markets.forEach(market => {
        const marketCount = countData.find(item => item.market === market);
        initialCounts[market] = marketCount ? marketCount.holiday_count : 0;
        console.log(`📊 Initial ${market} holiday count: ${initialCounts[market]}`);
      });
    } catch (e) {
      console.error('manageHolidays GET /holidays/count-by-market → failed to extract counts:', e.message);
      return;
    }

    // Step 2: Create full-day holiday for all markets
    const fullPayload = JSON.stringify({
      timeZone: "IST",
      market: markets, // Test all markets at once
      date: dateStr,
      reason: "K6 Test Full Day Holiday - All Markets",
      fullday: true,
      type: "Maintenance",
      startTime: null,
      endTime: null
    });

    const fullRes = http.post(`${ADMIN_BASE}/add-holiday/`, fullPayload, { headers });
    const fullOk = check(fullRes, {
      'manageHolidays POST /add-holiday → 201 (full-day all markets)': (r) => r.status === 201,
      'manageHolidays POST /add-holiday → has IDs (full-day all markets)': (r) => {
        try {
          const json = r.json();
          return Array.isArray(json.ids) && json.ids.length === markets.length;
        } catch (e) {
          console.error('manageHolidays POST /add-holiday → JSON parse error (full-day):', e.message);
          return false;
        }
      },
      'manageHolidays POST /add-holiday → success message (full-day all markets)': (r) => {
        try {
          const json = r.json();
          return json.message && json.message.includes("successfully");
        } catch (e) {
          console.error('manageHolidays POST /add-holiday → message check error (full-day):', e.message);
          return false;
        }
      }
    });

    if (!fullOk) {
      console.error('manageHolidays POST /add-holiday failed (full-day):', fullRes.status, fullRes.body);
      return;
    }

    let fullDayIds = [];
    try {
      const response = fullRes.json();
      fullDayIds = response.ids;
      createdHolidays.push(...fullDayIds);
      console.log(`✅ Created full-day holiday for all markets with IDs: ${fullDayIds.join(', ')}`);
    } catch (e) {
      console.error('manageHolidays POST /add-holiday → failed to extract IDs (full-day):', e.message);
      return;
    }

    // Step 3: Verify count increased after first holiday for all markets
    const afterFirstCountRes = http.get(`${ADMIN_BASE}/holidays/count-by-market/`, { headers });
    const afterFirstCountOk = check(afterFirstCountRes, {
      'manageHolidays GET /holidays/count-by-market → 200 (after first)': (r) => r.status === 200,
      'manageHolidays GET /holidays/count-by-market → all counts increased': (r) => {
        try {
          const countData = r.json();
          let allIncreased = true;
          markets.forEach(market => {
            const marketCount = countData.find(item => item.market === market);
            const currentCount = marketCount ? marketCount.holiday_count : 0;
            const expectedCount = initialCounts[market] + 1;
            console.log(`📊 After first holiday ${market} count: ${currentCount} (expected: ${expectedCount})`);
            if (currentCount !== expectedCount) {
              allIncreased = false;
            }
          });
          return allIncreased;
        } catch (e) {
          console.error('manageHolidays GET /holidays/count-by-market → count check error:', e.message);
          return false;
        }
      }
    });

    if (!afterFirstCountOk) {
      console.error('manageHolidays GET /holidays/count-by-market failed (after first):', afterFirstCountRes.status, afterFirstCountRes.body);
    }

    // Step 4: Create partial-day holiday for all markets (next day)
    const partialPayload = JSON.stringify({
      timeZone: "IST",
      market: markets, // Test all markets at once
      date: nextDay,
      reason: "K6 Test Partial Day Holiday - All Markets",
      fullday: false,
      type: "Maintenance",
      startTime: "08:00:00",
      endTime: "16:00:00"
    });

    const partialRes = http.post(`${ADMIN_BASE}/add-holiday/`, partialPayload, { headers });
    const partialOk = check(partialRes, {
      'manageHolidays POST /add-holiday → 201 (partial-day all markets)': (r) => r.status === 201,
      'manageHolidays POST /add-holiday → has IDs (partial-day all markets)': (r) => {
        try {
          const json = r.json();
          return Array.isArray(json.ids) && json.ids.length === markets.length;
        } catch (e) {
          console.error('manageHolidays POST /add-holiday → JSON parse error (partial-day):', e.message);
          return false;
        }
      },
      'manageHolidays POST /add-holiday → success message (partial-day all markets)': (r) => {
        try {
          const json = r.json();
          return json.message && json.message.includes("successfully");
        } catch (e) {
          console.error('manageHolidays POST /add-holiday → message check error (partial-day):', e.message);
          return false;
        }
      }
    });

    if (!partialOk) {
      console.error('manageHolidays POST /add-holiday failed (partial-day):', partialRes.status, partialRes.body);
      return;
    }

    let partialDayIds = [];
    try {
      const response = partialRes.json();
      partialDayIds = response.ids;
      createdHolidays.push(...partialDayIds);
      console.log(`✅ Created partial-day holiday for all markets with IDs: ${partialDayIds.join(', ')}`);
    } catch (e) {
      console.error('manageHolidays POST /add-holiday → failed to extract IDs (partial-day):', e.message);
      return;
    }

    // Step 5: Verify count increased after second holiday for all markets
    const afterSecondCountRes = http.get(`${ADMIN_BASE}/holidays/count-by-market/`, { headers });
    const afterSecondCountOk = check(afterSecondCountRes, {
      'manageHolidays GET /holidays/count-by-market → 200 (after second)': (r) => r.status === 200,
      'manageHolidays GET /holidays/count-by-market → all counts increased again': (r) => {
        try {
          const countData = r.json();
          let allIncreased = true;
          markets.forEach(market => {
            const marketCount = countData.find(item => item.market === market);
            const currentCount = marketCount ? marketCount.holiday_count : 0;
            const expectedCount = initialCounts[market] + 2;
            console.log(`📊 After second holiday ${market} count: ${currentCount} (expected: ${expectedCount})`);
            if (currentCount !== expectedCount) {
              allIncreased = false;
            }
          });
          return allIncreased;
        } catch (e) {
          console.error('manageHolidays GET /holidays/count-by-market → count check error:', e.message);
          return false;
        }
      }
    });

    if (!afterSecondCountOk) {
      console.error('manageHolidays GET /holidays/count-by-market failed (after second):', afterSecondCountRes.status, afterSecondCountRes.body);
    }

    // Step 6: Delete all full-day holidays
    let fullDayDeletionSuccess = true;
    fullDayIds.forEach((holidayId, index) => {
      const delFull = http.del(`${ADMIN_BASE}/holidays/${holidayId}/`, null, { headers });
      const delFullOk = check(delFull, {
        [`manageHolidays DELETE /holidays/{id} → 200 (full-day ${index + 1})`]: (r) => r.status === 200,
        [`manageHolidays DELETE /holidays/{id} → success message (full-day ${index + 1})`]: (r) => {
          try {
            const json = r.json();
            return json.message && json.message.includes("successfully");
          } catch (e) {
            console.error(`manageHolidays DELETE /holidays/{id} → message check error (full-day ${index + 1}):`, e.message);
            return false;
          }
        }
      });

      if (!delFullOk) {
        console.error(`manageHolidays DELETE /holidays/{id} failed (full-day ${index + 1}):`, delFull.status, delFull.body);
        fullDayDeletionSuccess = false;
      } else {
        console.log(`✅ Deleted full-day holiday with ID: ${holidayId} (${markets[index]})`);
      }
    });

    // Step 7: Verify count decreased after full-day deletions for all markets
    const afterFirstDelCountRes = http.get(`${ADMIN_BASE}/holidays/count-by-market/`, { headers });
    const afterFirstDelCountOk = check(afterFirstDelCountRes, {
      'manageHolidays GET /holidays/count-by-market → 200 (after full-day deletions)': (r) => r.status === 200,
      'manageHolidays GET /holidays/count-by-market → all counts decreased': (r) => {
        try {
          const countData = r.json();
          let allDecreased = true;
          markets.forEach(market => {
            const marketCount = countData.find(item => item.market === market);
            const currentCount = marketCount ? marketCount.holiday_count : 0;
            const expectedCount = initialCounts[market] + 1; // Should have 1 remaining (partial-day)
            console.log(`📊 After full-day deletion ${market} count: ${currentCount} (expected: ${expectedCount})`);
            if (currentCount !== expectedCount) {
              allDecreased = false;
            }
          });
          return allDecreased;
        } catch (e) {
          console.error('manageHolidays GET /holidays/count-by-market → count check error:', e.message);
          return false;
        }
      }
    });

    if (!afterFirstDelCountOk) {
      console.error('manageHolidays GET /holidays/count-by-market failed (after full-day deletions):', afterFirstDelCountRes.status, afterFirstDelCountRes.body);
    }

    // Step 8: Delete all partial-day holidays
    partialDayIds.forEach((holidayId, index) => {
      const delPartial = http.del(`${ADMIN_BASE}/holidays/${holidayId}/`, null, { headers });
      const delPartialOk = check(delPartial, {
        [`manageHolidays DELETE /holidays/{id} → 200 (partial-day ${index + 1})`]: (r) => r.status === 200,
        [`manageHolidays DELETE /holidays/{id} → success message (partial-day ${index + 1})`]: (r) => {
          try {
            const json = r.json();
            return json.message && json.message.includes("successfully");
          } catch (e) {
            console.error(`manageHolidays DELETE /holidays/{id} → message check error (partial-day ${index + 1}):`, e.message);
            return false;
          }
        }
      });

      if (!delPartialOk) {
        console.error(`manageHolidays DELETE /holidays/{id} failed (partial-day ${index + 1}):`, delPartial.status, delPartial.body);
      } else {
        console.log(`✅ Deleted partial-day holiday with ID: ${holidayId} (${markets[index]})`);
      }
    });

    // Step 9: Verify count returned to initial values after final deletion for all markets
    const finalCountRes = http.get(`${ADMIN_BASE}/holidays/count-by-market/`, { headers });
    const finalCountOk = check(finalCountRes, {
      'manageHolidays GET /holidays/count-by-market → 200 (final)': (r) => r.status === 200,
      'manageHolidays GET /holidays/count-by-market → all counts returned to initial': (r) => {
        try {
          const countData = r.json();
          let allReturned = true;
          markets.forEach(market => {
            const marketCount = countData.find(item => item.market === market);
            const currentCount = marketCount ? marketCount.holiday_count : 0;
            const expectedCount = initialCounts[market];
            console.log(`📊 Final ${market} count: ${currentCount} (expected: ${expectedCount})`);
            if (currentCount !== expectedCount) {
              allReturned = false;
            }
          });
          return allReturned;
        } catch (e) {
          console.error('manageHolidays GET /holidays/count-by-market → count check error:', e.message);
          return false;
        }
      }
    });

    if (!finalCountOk) {
      console.error('manageHolidays GET /holidays/count-by-market failed (final):', finalCountRes.status, finalCountRes.body);
    } else {
      console.log(`✅ Holiday count verification complete: All market counts returned to initial values`);
      console.log(`🏢 Tested markets: ${markets.join(', ')}`);
    }
  });
}

export function badManageHolidays() {
  group('❌ Manage Holidays - Bad Scenarios', () => {

    // Test helper function for POST requests
    const test = (desc, url, payload, testHeaders, expectedStatus = 400) => {
      const res = http.post(url, payload, { headers: testHeaders });
      const ok = check(res, {
        [`${desc} → ${expectedStatus}+`]: (r) => r.status >= expectedStatus,
      });
      if (!ok) {
        console.error(`${desc} failed →`, res.status, res.body);
      }
    };

    // Test helper function for DELETE requests
    const testDelete = (desc, url, testHeaders, expectedStatus = 400) => {
      const res = http.del(url, null, { headers: testHeaders });
      const ok = check(res, {
        [`${desc} → ${expectedStatus}+`]: (r) => r.status >= expectedStatus,
      });
      if (!ok) {
        console.error(`${desc} failed →`, res.status, res.body);
      }
    };

    // Test helper function for GET requests
    const testGet = (desc, url, testHeaders, expectedStatus = 400) => {
      const res = http.get(url, { headers: testHeaders });
      const ok = check(res, {
        [`${desc} → ${expectedStatus}+`]: (r) => r.status >= expectedStatus,
      });
      if (!ok) {
        console.error(`${desc} failed →`, res.status, res.body);
      }
    };

    // Test missing fields
    test(
      'manageHolidays POST /add-holiday → missing fields',
      `${ADMIN_BASE}/add-holiday/`,
      JSON.stringify({}),
      headers
    );

    // Test invalid date format
    test(
      'manageHolidays POST /add-holiday → invalid date',
      `${ADMIN_BASE}/add-holiday/`,
      JSON.stringify({
        timeZone: "IST",
        market: ["CRYPTO"],
        date: "invalid-date",
        reason: "Test",
        fullday: true,
        type: "Maintenance"
      }),
      headers
    );

    // Test invalid time format
    test(
      'manageHolidays POST /add-holiday → invalid time',
      `${ADMIN_BASE}/add-holiday/`,
      JSON.stringify({
        timeZone: "IST",
        market: ["CRYPTO"],
        date: "2025-12-01",
        reason: "Test",
        fullday: false,
        type: "Maintenance",
        startTime: "25:00:00",
        endTime: "99:99:99"
      }),
      headers
    );

    // Test invalid market
    test(
      'manageHolidays POST /add-holiday → invalid market',
      `${ADMIN_BASE}/add-holiday/`,
      JSON.stringify({
        timeZone: "IST",
        market: ["INVALID_MARKET"],
        date: "2025-12-01",
        reason: "Test",
        fullday: true,
        type: "Maintenance"
      }),
      headers
    );

    // Test malformed JSON
    test(
      'manageHolidays POST /add-holiday → malformed JSON',
      `${ADMIN_BASE}/add-holiday/`,
      '{"timeZone": "IST", "market": invalid json',
      headers
    );

    // Test missing authentication
    test(
      'manageHolidays POST /add-holiday → no auth',
      `${ADMIN_BASE}/add-holiday/`,
      JSON.stringify({
        timeZone: "IST",
        market: ["CRYPTO"],
        date: "2025-12-30",
        reason: "Test",
        fullday: true,
        type: "Maintenance"
      }),
      noAuthHeaders,
      403
    );

    // Test invalid authentication
    test(
      'manageHolidays POST /add-holiday → bad token',
      `${ADMIN_BASE}/add-holiday/`,
      JSON.stringify({
        timeZone: "IST",
        market: ["CRYPTO"],
        date: "2025-12-31",
        reason: "Test",
        fullday: true,
        type: "Maintenance"
      }),
      badHeaders,
      401
    );

    // Test GET count with bad auth
    testGet(
      'manageHolidays GET /holidays/count-by-market → no auth',
      `${ADMIN_BASE}/holidays/count-by-market/`,
      noAuthHeaders,
      403
    );

    testGet(
      'manageHolidays GET /holidays/count-by-market → bad token',
      `${ADMIN_BASE}/holidays/count-by-market/`,
      badHeaders,
      401
    );

    // Test DELETE with bad auth
    testDelete(
      'manageHolidays DELETE /holidays/{id} → no auth',
      `${ADMIN_BASE}/holidays/999999/`,
      noAuthHeaders,
      403
    );

    testDelete(
      'manageHolidays DELETE /holidays/{id} → bad token',
      `${ADMIN_BASE}/holidays/999999/`,
      badHeaders,
      401
    );

    // Test DELETE non-existent holiday
    testDelete(
      'manageHolidays DELETE /holidays/{id} → non-existent',
      `${ADMIN_BASE}/holidays/999999999/`,
      headers,
      404
    );

    // Test duplicate holiday creation for all markets
    const duplicateDate = '2025-12-25';
    const markets = Object.keys(MARKET_TIMINGS);
    const duplicatePayload = JSON.stringify({
      timeZone: "IST",
      market: markets, // Test all markets
      date: duplicateDate,
      reason: "Duplicate Test - All Markets",
      fullday: true,
      type: "Maintenance"
    });

    // Create first holiday for all markets
    const res1 = http.post(`${ADMIN_BASE}/add-holiday/`, duplicatePayload, { headers });

    // Try to create duplicate for all markets
    test(
      'manageHolidays POST /add-holiday → duplicate holiday (all markets)',
      `${ADMIN_BASE}/add-holiday/`,
      duplicatePayload,
      headers,
      409 // Conflict status code for duplicates
    );

    // Clean up the first holiday if it was created successfully
    if (res1.status === 201) {
      try {
        const response = res1.json();
        if (response.ids && response.ids.length > 0) {
          response.ids.forEach((holidayId, index) => {
            http.del(`${ADMIN_BASE}/holidays/${holidayId}/`, null, { headers });
            console.log(`🧹 Cleaned up test holiday with ID: ${holidayId} (${markets[index]})`);
          });
        }
      } catch (e) {
        console.log('manageHolidays: Could not clean up test holidays (expected if creation failed)');
      }
    }
  });
}
