import { sleep, check, group } from 'k6';
import http from 'k6/http';
import { BASE_URL, ADMIN_BASE, EXCHANGE } from '../config/config.js';
import { getUserSafe, safeJson } from '../utils/csvUserManager.js';

// executor: 'ramping-vus',
//     startVUs: 0,
//     stages: [
//       { duration: '30s', target: 10 },
//       { duration: '1m', target: 10 },
//       { duration: '30s', target: 0 },
//     ],
//     exec: 'buyGoodStopLossOrderFlow',

export const options = {
  scenarios: {
    stoploss_order_good: {
      executor: 'per-vu-iterations',
      vus: 1,
      iterations: 1,
      exec: 'buyGoodStopLossOrderFlow',
    },
    stoploss_order_negative: {
      executor: 'per-vu-iterations',
      vus: 1,
      iterations: 1,
      exec: 'buyBadStopLossOrderFlow',
    },
  },
};

export function buyGoodStopLossOrderFlow() {
  const { user_id, password, token } = getUserSafe();

  if (!user_id || !token) {
    console.error(`❌ No valid user available for VU ${__VU}`);
    return;
  }

  const headers = {
    'Content-Type': 'application/json',
    Authorization: `Bearer ${token}`,
  };

  group(`✅ STOPLOSS Order for ${user_id}`, () => {
    // 1. Wallet BEFORE
    const walletBeforeRes = http.get(`${ADMIN_BASE}/wallets/balance/${user_id}`, { headers });
    const walletBeforeOk = check(walletBeforeRes, { 'wallet before → 200': (r) => r.status === 200 });
    if (!walletBeforeOk) console.error('📥 Wallet Before:', walletBeforeRes.status, walletBeforeRes.body);

    const walletBeforeJson = safeJson(walletBeforeRes);
    const netBefore = parseFloat(walletBeforeJson?.wallets?.find(w => w.exchange === EXCHANGE)?.net_balance || 0);

    // 2. Get Instrument
    const instrumentRes = http.post(`${BASE_URL}/api/user/getInstruments`, JSON.stringify({
      exchange: EXCHANGE,
      searchPattern: '',
      page: 1,
      pageSize: 1,
    }), { headers });
    const instrumentOk = check(instrumentRes, { 'buyStopLossOrder_getInstruments → 200': (r) => r.status === 200 });
    if (!instrumentOk) console.error('buyStopLossOrder_getInstruments:', instrumentRes.status, instrumentRes.body);

    const instrumentJson = safeJson(instrumentRes);
    const instrument = instrumentJson?.instruments?.[0];
    if (!instrument) {
      console.error("buyStopLossOrder_getInstruments No instrument found");
      return;
    }

    const {
      FinancialInstrumentID: instrumentId,
      LastPrice: lastPriceRaw,
      UpperCkt: upperCktRaw
    } = instrument;

    const lastPrice = parseFloat(lastPriceRaw);
    const upperCkt = parseFloat(upperCktRaw);

    console.log(`🎯 Using instrumentId: ${instrumentId}, LastPrice: ${lastPriceRaw}, UpperCkt: ${upperCktRaw}`);

    // ✅ Guard clause for invalid price values
    if (!lastPrice || isNaN(lastPrice) || lastPrice <= 0) {
      console.error(`buyStopLossOrder Skipping: Invalid instrument price data → LastPrice: ${lastPriceRaw}`);
      return;
    }

    // Check if upper circuit is valid, if not, use a safe multiplier approach
    if (!upperCkt || isNaN(upperCkt) || upperCkt <= 0) {
      console.error(`buyStopLossOrder Skipping: Invalid instrument price data → LastPrice: ${lastPriceRaw}, UpperCkt: ${upperCktRaw}`);
      return;
    }

    const triggerPrice = parseFloat((lastPrice * 1.001).toFixed(2));

    // ✅ Skip test if trigger price hits or exceeds upper circuit
    if (triggerPrice >= upperCkt) {
      console.error(`buyStopLossOrder triggerPrice (${triggerPrice}) ≥ upper circuit (${upperCkt}) → Skipping`);
      return;
    }

    console.log(`🎯 Instrument ID: ${instrumentId}, LastPrice: ${lastPrice}, UpperCkt: ${upperCkt}, TriggerPrice: ${triggerPrice}`);


    // 3. Place Order
    const orderPayload = {
      instrumentId,
      exchange: EXCHANGE,
      quantity: 1,
      OrderType: 'STOPLOSS',
      buy: true,
      triggerPrice,
    };
    const orderRes = http.post(`${BASE_URL}/api/user/makeOrder`, JSON.stringify(orderPayload), { headers });
    const orderOk = check(orderRes, {
      'buyStopLossOrder_makeOrder → 200': (r) => r.status === 200,
      'buyStopLossOrder_makeOrder → success': (r) => r.json()?.status === 'success',
      'buyStopLossOrder_Funds blocked > 0': (r) => parseFloat(r.json()?.message?.match(/Funds blocked: ([\d.]+)/)?.[1] || 0) > 0,
    });
    if (!orderOk) {
      console.error('buyStopLossOrder_makeOrder Payload:', JSON.stringify(orderPayload, null, 2));
      console.error('buyStopLossOrder_makeOrder Response:', orderRes.status, orderRes.body);
      return;
    }

    sleep(1);

    // 4. Wallet AFTER
    const walletAfterRes = http.get(`${ADMIN_BASE}/wallets/balance/${user_id}`, { headers });
    const walletAfterOk = check(walletAfterRes, { 'buyStopLossOrder_wallet after → 200': (r) => r.status === 200 });
    if (!walletAfterOk) console.error('buyStopLossOrder_Wallet After:', walletAfterRes.status, walletAfterRes.body);

    const walletAfterJson = safeJson(walletAfterRes);
    const netAfter = parseFloat(walletAfterJson?.wallets?.find(w => w.exchange === EXCHANGE)?.net_balance || 0);

    // 5. Check short position
    const portfolioRes = http.post(`${BASE_URL}/api/user/getPortfolio`, JSON.stringify({ exchange: EXCHANGE }), { headers });
    const portfolioOk = check(portfolioRes, { 'buyStopLossOrder_getPortfolio → 200': (r) => r.status === 200 });
    if (!portfolioOk) console.error('buyStopLossOrder_getPortfolio:', portfolioRes.status, portfolioRes.body);

    const portfolioJson = safeJson(portfolioRes);
    const position = portfolioJson?.portfolios?.find(p => p.FinancialInstrumentID === instrumentId);
    const isShortCovered = position && position.Quantity < 0;
    console.log(`📊 Position check: Qty = ${position?.Quantity ?? 'N/A'}, isShort = ${isShortCovered}`);

    const stopLossLabel = `buyStopLossOrder_netBalance ${isShortCovered ? 'unchanged (short cover)' : 'reduced'} after STOPLOSS`;

    const stopLossCheck = check({}, {
      [stopLossLabel]: () => isShortCovered ? netAfter === netBefore : netAfter < netBefore,
    });


    if (!stopLossCheck) {
      console.error(
        `❌ CHECK FAILED: ${stopLossLabel}\n` +
        `Expected: ${isShortCovered ? 'netAfter === netBefore' : 'netAfter < netBefore'}\n` +
        `Actual: netBefore = ${netBefore}, netAfter = ${netAfter}`
      );
    }


    // 6. openOrders check
    const openRes = http.post(`${BASE_URL}/api/user/openOrders`, JSON.stringify({ exchange: EXCHANGE }), { headers });
    const openOk = check(openRes, { 'buyStopLossOrder_openOrders → 200': (r) => r.status === 200 });
    if (!openOk) console.error('buyStopLossOrder_openOrders:', openRes.status, openRes.body);

    const openJson = safeJson(openRes);
    const order = openJson?.orders?.find(o => o.instrumentId === instrumentId && o.orderType === 'STOPLOSS');
    if (!order) {
      console.error("buyStopLossOrder_STOPLOSS order not found in openOrders");
      return;
    }

    const orderId = order.ID;

    // 7. Modify
    const modifyPayload = {
      exchange: EXCHANGE,
      orderId,
      triggerPrice: triggerPrice + 1,
      quantity: 2,
    };
    const modifyRes = http.post(`${BASE_URL}/api/user/modifyOrder`, JSON.stringify(modifyPayload), { headers });
    const modifyOk = check(modifyRes, {
      'buyStopLossOrder_modifyOrder → 200': (r) => r.status === 200,
      'buyStopLossOrder_modifyOrder → success': (r) => r.json()?.status === 'success',
    });
    if (!modifyOk) {
      console.error('buyStopLossOrder_modifyOrder Payload:', JSON.stringify(modifyPayload, null, 2));
      console.error('buyStopLossOrder_modifyOrder Response:', modifyRes.status, modifyRes.body);
      return;
    }

    // 8. Cancel
    const cancelPayload = { exchange: EXCHANGE, orderId };
    const cancelRes = http.post(`${BASE_URL}/api/user/cancelOrder`, JSON.stringify(cancelPayload), { headers });
    const cancelOk = check(cancelRes, {
      'buyStopLossOrder_cancelOrder → 200': (r) => r.status === 200,
      'buyStopLossOrder_cancelOrder → success': (r) => r.json()?.status === 'success',
    });
    if (!cancelOk) {
      console.error('buyStopLossOrder_cancelOrder Payload:', JSON.stringify(cancelPayload, null, 2));
      console.error('buyStopLossOrder_cancelOrder Response:', cancelRes.status, cancelRes.body);
    }

    sleep(1);
  });
}



export function buyBadStopLossOrderFlow() {
  const { user_id, password, token } = getUserSafe();

  if (!user_id || !token) {
    console.error(`❌ No valid user available for VU ${__VU}`);
    return;
  }

  const headers = {
    'Content-Type': 'application/json',
    Authorization: `Bearer ${token}`,
  };

  group('❌ STOPLOSS Order - Negative Scenarios', () => {
    const invalidId = 999999999999;

    const tryOrder = (label, payload) => {
      const res = http.post(`${BASE_URL}/api/user/makeOrder`, JSON.stringify(payload), { headers });
      const ok = check(res, {
        [label]: (r) => r.status >= 400 || r.json().status !== 'success',
      });
      if (!ok) {
        console.error(`📤 ${label} Payload:`, JSON.stringify(payload, null, 2));
        console.error(`📥 ${label} Response:`, res.status, res.body);
      }
    };

    tryOrder('buyStopLossOrder_STOPLOSS → 400 on missing triggerPrice', {
      instrumentId: invalidId,
      exchange: EXCHANGE,
      quantity: 1,
      OrderType: 'STOPLOSS',
      buy: true,
    });

    tryOrder('buyStopLossOrder_STOPLOSS → fail on invalid instrumentId', {
      instrumentId: invalidId,
      exchange: EXCHANGE,
      quantity: 1,
      OrderType: 'STOPLOSS',
      buy: true,
      triggerPrice: 9999,
    });

    const instRes = http.post(`${BASE_URL}/api/user/getInstruments`, JSON.stringify({
      exchange: EXCHANGE,
      searchPattern: '',
      page: 1,
      pageSize: 1,
    }), { headers });

    const instrumentJson = safeJson(instRes);
    const instrument = instrumentJson?.instruments?.[0];
    if (!instrument) {
      console.error("No instrument found for buyStopLossOrder_STOPLOSS negative test");
      return;
    }

    const tooLow = parseFloat((instrument.LastPrice * 0.00001).toFixed(2));
    // Use a safe high value if UpperCkt is invalid
    const upperCkt = parseFloat(instrument.UpperCkt) || 0;
    const tooHigh = upperCkt > 0 ? parseFloat((upperCkt + 10).toFixed(2)) : parseFloat((instrument.LastPrice * 2).toFixed(2));

    tryOrder('buyStopLossOrder_STOPLOSS → 400 on too low triggerPrice', {
      instrumentId: instrument.FinancialInstrumentID,
      exchange: EXCHANGE,
      quantity: 1,
      OrderType: 'STOPLOSS',
      buy: true,
      triggerPrice: tooLow,
    });

    tryOrder('buyStopLossOrder_STOPLOSS → 400 on triggerPrice above upper circuit', {
      instrumentId: instrument.FinancialInstrumentID,
      exchange: EXCHANGE,
      quantity: 1,
      OrderType: 'STOPLOSS',
      buy: true,
      triggerPrice: tooHigh,
    });

    sleep(1);
  });
}

