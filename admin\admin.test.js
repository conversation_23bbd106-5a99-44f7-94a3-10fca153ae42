import http from 'k6/http';
import { check, group } from 'k6';
import * as cfg from '../config/config.js';

// Scenario imports
import { userMappingGoodFlow } from './enableUser.test.js';
import { userMappingNegativeFlow } from './enablingUserNegativeFlow.test.js';
import { goodWalletEnableAndAddingBalanceScenario, badWalletEnableAndAddingBalanceScenario } from './walletEnableAndAddingBalance.test.js';
import { goodUsers, badUsers } from './admin-users.test.js';
import { goodAdminLogin, badAdminLogin } from './admin-login.test.js';
import { goodUserTransactions, badUserTransactions } from './userTransactions.test.js';
import { goodUserTrades, badUserTrades } from './userTrades.test.js';
import { goodTransactionsStatusCount, badTransactionsStatusCount } from './transactionsStatusCount.test.js';
import { positiveExchangeEnableFlow, negativeExchangeEnableCases } from './userExchangesEnableFlow.test.js';
import { goodUserCounts, badUserCounts } from './userCounts.test.js';
import { goodPositionsSummary, badPositionsSummary } from './positionsSummary.test.js';
import { goodUserLimitsAndLeverage, badUserLimitsAndLeverage } from './userLimitsAndLeverage.test.js';
import { goodStopLossToggle, badStopLossToggle } from './stopLossToggle.test.js';
import { goodCreateSubagentWithConfigCopy, badCreateSubagentWithConfigCopy } from './createSubagentWithConfigCopy.test.js';
import { goodCreateAgentAndCopyConfig, badCreateAgentAndCopyConfig } from './createAgentAndCopyConfig.test.js';
import { goodManageHolidays, badManageHolidays } from './manageHolidays.test.js';
import { goodBanScript, badBanScript } from './banScriptDynamic.test.js';
// import { loginFromCsv } from './loginFromCsv.test.js';
// import { createUser } from './createUser.test.js';

/* -------------------------------------- */
/* ✅ Scenario selection via env only     */
/* -------------------------------------- */
const SCENARIOS = __ENV.ADMIN_SCENARIOS
  ? __ENV.ADMIN_SCENARIOS.split(",")
  : [];

if (!SCENARIOS.length) {
  throw new Error("❌ ADMIN_SCENARIOS is required but not provided.");
}

export const options = {
  vus: 1,
  iterations: 1,
};

export function setup() {
  console.log(`🚀 Running ADMIN tests`);
  console.log(`📋 Selected scenarios: ${SCENARIOS.join(", ")}`);
}

/* -------------------------------------- */
/* ✅ Scenario execution                  */
/* -------------------------------------- */
export default function () {
  group('🔥 Running Admin Test Scenarios', () => {
    // Good flows
    if (SCENARIOS.includes("userMappingGoodFlow")) userMappingGoodFlow();
    if (SCENARIOS.includes("goodWalletEnableAndAddingBalanceScenario")) goodWalletEnableAndAddingBalanceScenario();
    if (SCENARIOS.includes("goodUsers")) goodUsers();
    if (SCENARIOS.includes("goodAdminLogin")) goodAdminLogin();
    if (SCENARIOS.includes("goodUserTransactions")) goodUserTransactions();
    if (SCENARIOS.includes("goodUserTrades")) goodUserTrades();
    if (SCENARIOS.includes("goodTransactionsStatusCount")) goodTransactionsStatusCount();
    if (SCENARIOS.includes("positiveExchangeEnableFlow")) positiveExchangeEnableFlow();
    if (SCENARIOS.includes("goodUserCounts")) goodUserCounts();
    if (SCENARIOS.includes("goodPositionsSummary")) goodPositionsSummary();
    if (SCENARIOS.includes("goodUserLimitsAndLeverage")) goodUserLimitsAndLeverage();
    if (SCENARIOS.includes("goodStopLossToggle")) goodStopLossToggle();
    if (SCENARIOS.includes("goodCreateSubagentWithConfigCopy")) goodCreateSubagentWithConfigCopy();
    if (SCENARIOS.includes("goodCreateAgentAndCopyConfig")) goodCreateAgentAndCopyConfig();
    if (SCENARIOS.includes("goodManageHolidays")) goodManageHolidays();
    if (SCENARIOS.includes("goodBanScript")) goodBanScript();
    // if (SCENARIOS.includes("loginFromCsv")) loginFromCsv();
    // if (SCENARIOS.includes("createUser")) createUser();

    // Bad flows
    if (SCENARIOS.includes("badWalletEnableAndAddingBalanceScenario")) badWalletEnableAndAddingBalanceScenario();
    if (SCENARIOS.includes("badUsers")) badUsers();
    if (SCENARIOS.includes("badAdminLogin")) badAdminLogin();
    if (SCENARIOS.includes("badUserTransactions")) badUserTransactions();
    if (SCENARIOS.includes("badUserTrades")) badUserTrades();
    if (SCENARIOS.includes("badTransactionsStatusCount")) badTransactionsStatusCount();
    if (SCENARIOS.includes("negativeExchangeEnableCases")) negativeExchangeEnableCases();
    if (SCENARIOS.includes("badUserCounts")) badUserCounts();
    if (SCENARIOS.includes("badPositionsSummary")) badPositionsSummary();
    if (SCENARIOS.includes("badUserLimitsAndLeverage")) badUserLimitsAndLeverage();
    if (SCENARIOS.includes("badStopLossToggle")) badStopLossToggle();
    if (SCENARIOS.includes("badCreateSubagentWithConfigCopy")) badCreateSubagentWithConfigCopy();
    if (SCENARIOS.includes("badCreateAgentAndCopyConfig")) badCreateAgentAndCopyConfig();
    if (SCENARIOS.includes("badManageHolidays")) badManageHolidays();
    if (SCENARIOS.includes("badBanScript")) badBanScript();
    if (SCENARIOS.includes("userMappingNegativeFlow")) userMappingNegativeFlow();
  });
}
