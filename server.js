const express = require('express');
const path = require('path');
const fs = require('fs');
const readline = require("readline");
const { exec } = require('child_process');
const morgan = require('morgan');
const swaggerUi = require('swagger-ui-express');
const swaggerDocs = require('./swagger'); // Assumes swagger.js exists
const loadFreshConfig = require('./utils/loadFreshConfig');


const app = express();
const PORT = 4000;

// Error listeners for safety
process.on('uncaughtException', (err) => {
  console.error('Uncaught Exception:', err);
});
process.on('unhandledRejection', (reason) => {
  console.error('Unhandled Rejection:', reason);
});

// Paths
const logsDir = path.join(__dirname, 'logs');
const configFilePath = path.join(__dirname, 'config', 'config.js');
const userMainFile = path.join(__dirname, "scenarios", "main.test.js");
const adminMainFile = path.join(__dirname, "admin", "admin.test.js");

console.log('Logs directory:', logsDir);
console.log('Config file path:', configFilePath);
console.log('Main JS file path:', userMainFile);
console.log('Admin JS file path:', adminMainFile);

// Middleware
app.use(express.json());
app.use(morgan('combined'));
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerDocs));

// -----------------------------------
// Config APIs
// -----------------------------------
app.get('/api/config', (req, res) => {
  fs.readFile(configFilePath, 'utf8', (err, data) => {
    if (err) {
      console.error('Error reading config file:', err);
      return res.status(500).json({ error: 'Failed to read config file' });
    }
    try {
      const config = eval(data); // DANGER: Make sure config is trusted
      res.status(200).json(config);
    } catch (parseError) {
      console.error('Error parsing config:', parseError);
      res.status(500).json({ error: 'Invalid config content' });
    }
  });
});

app.put('/api/config', (req, res) => {
  const updatedConfig = req.body;
  const configString = `module.exports = ${JSON.stringify(updatedConfig, null, 2)};`;
  fs.writeFile(configFilePath, configString, 'utf8', (err) => {
    if (err) {
      console.error('Error writing config file:', err);
      return res.status(500).json({ error: 'Failed to update config file' });
    }
    res.status(200).json({ message: 'Configuration updated successfully' });
  });
});

// -----------------------------------
// Logs APIs
// -----------------------------------
app.get('/api/logs', (req, res) => {
  const { type } = req.query; // ?type=admin|user
  fs.readdir(logsDir, (err, files) => {
    if (err) return res.status(500).json({ error: 'Failed to read logs directory' });
    const txtFiles = files.filter(file => file.endsWith('.txt'));
    const filtered = type ? txtFiles.filter(f => f.startsWith(`${type}-`)) : txtFiles;
    const names = filtered.map(f => f.replace('.txt', ''));
    res.status(200).json({ logs: names });
  });
});

app.get('/api/log/:logName', (req, res) => {
  const filePath = path.join(logsDir, `${req.params.logName}.txt`);
  if (!fs.existsSync(filePath)) return res.status(404).json({ error: 'Log file not found' });

  fs.readFile(filePath, 'utf8', (err, data) => {
    if (err) return res.status(500).json({ error: 'Failed to read log file' });
    res.status(200).json({ log: data });
  });
});

app.post("/api/search-log", async (req, res) => {
  const { logFile, keyword } = req.body;
  if (!logFile || !keyword)
    return res.status(400).json({ success: false, error: "Missing logFile or keyword" });

  const fullPath = path.join(logsDir, logFile);
  if (!fs.existsSync(fullPath))
    return res.status(404).json({ success: false, error: "Log file not found" });

  try {
    const matchedLines = [];
    const rl = readline.createInterface({
      input: fs.createReadStream(fullPath),
      crlfDelay: Infinity,
    });

    for await (const line of rl) {
      if (line.toLowerCase().includes("error") && line.toLowerCase().includes(keyword.toLowerCase())) {
        matchedLines.push(line);
      }
    }

    res.json({ success: true, count: matchedLines.length, lines: matchedLines });
  } catch (err) {
    console.error("Search error:", err);
    res.status(500).json({ success: false, error: "Internal read error" });
  }
});

app.delete("/api/logs", (req, res) => {
  const { logFiles } = req.body;
  if (!Array.isArray(logFiles) || logFiles.length === 0)
    return res.status(400).json({ error: "body.logFiles must be a non-empty array" });

  const results = logFiles.map(file => {
    const fullPath = path.join(logsDir, `${file}.txt`);
    if (!fs.existsSync(fullPath)) return { file, status: "not_found" };
    try {
      fs.unlinkSync(fullPath);
      return { file, status: "deleted" };
    } catch (e) {
      return { file, status: "error", message: e.message };
    }
  });

  res.json({ success: true, results });
});

// -----------------------------------
// Scenarios APIs
// -----------------------------------
app.get("/api/scenarios", (req, res) => {
  const file = path.join(__dirname, 'scenarios', 'scenarios.json');
  fs.readFile(file, "utf8", (err, data) => {
    if (err) return res.status(500).json({ error: "Cannot load scenarios.json" });
    try {
      const parsed = JSON.parse(data);
      const all = [...parsed.GoodScenarios, ...parsed.BadScenarios, ...(parsed.OtherScenarios || [])];
      res.json({ success: true, scenarios: all });
    } catch (e) {
      res.status(500).json({ error: "Malformed scenarios.json" });
    }
  });
});

app.get("/api/admin-scenarios", (req, res) => {
  const file = path.join(__dirname, 'admin', 'adminScenarios.json');
  fs.readFile(file, "utf8", (err, data) => {
    if (err) return res.status(500).json({ error: "Cannot load adminScenarios.json" });
    try {
      const parsed = JSON.parse(data);
      const all = [...parsed.GoodScenarios, ...parsed.BadScenarios];
      res.json({ success: true, scenarios: all });
    } catch (e) {
      res.status(500).json({ error: "Malformed adminScenarios.json" });
    }
  });
});






const getTimestampLabel = (scenarios) =>
  scenarios.join("+").substring(0, 60).replace(/[^a-zA-Z0-9+_-]/g, "");

const getLogFilePath = (type, label, mode) => {
  const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
  return path.join(logsDir, `${type}-${mode}-${label}-${timestamp}.txt`);
};

app.post("/api/run-k6", (req, res) => {
  const {
    scenarios,
    testMode = "scenario"
  } = req.body;

  if (!Array.isArray(scenarios) || scenarios.length === 0) {
    return res.status(400).json({ error: "body.scenarios must be a non-empty array" });
  }

  if (!["scenario", "load"].includes(testMode)) {
    return res.status(400).json({ error: "testMode must be 'scenario' or 'load'" });
  }

  try {
    // 🔄 Fresh load of config.js
    const config = loadFreshConfig();

    const env = {
      ...process.env,
      SCENARIOS: scenarios.join(","),
      TEST_MODE: testMode
    };

    if (testMode === "scenario") {
      env.USER_ID = config.USER_ID;
      env.AUTH_TOKEN = config.AUTH_TOKEN;
      console.log(`🔐 Using USER_ID: ${env.USER_ID}`);
      console.log(`🔐 Using AUTH_TOKEN: ${env.AUTH_TOKEN}`);
    }

    let k6Options = "";
    if (testMode === "load") {
      console.log("⚙️ Loaded RAMP_CONFIG:", config.RAMP_CONFIG);
      const stages = config.RAMP_CONFIG.stages.map(stage =>
        `--stage ${stage.duration}:${stage.target}`
      ).join(" ");
      k6Options = stages;
      console.log(`🚀 Running user load test: ${k6Options}`);
    } else {
      console.log(`🧪 Running user scenario test for: ${scenarios.join(", ")}`);
    }

    const label = getTimestampLabel(scenarios);
    const logFile = getLogFilePath("users", label, testMode);
    const fullCmd = `k6 run ${k6Options} "${userMainFile}" > "${logFile}" 2>&1`;

    console.log(`📝 Executing: ${fullCmd}`);

    exec(fullCmd, { env }, (err) => {
      if (err) {
        return res.status(500).json({ error: "k6 run failed", details: err.message });
      }
      res.json({
        success: true,
        message: `${testMode} test completed successfully`,
        logFile: path.basename(logFile, ".txt")
      });
    });

  } catch (error) {
    console.error("❌ User K6 API error:", error);
    res.status(500).json({ error: "Internal server error", details: error.message });
  }
});


app.post("/api/run-admin-k6", (req, res) => {
  const config = loadFreshConfig();
  const { scenarios, testMode = "scenario" } = req.body;

  if (!Array.isArray(scenarios) || scenarios.length === 0)
    return res.status(400).json({ error: "body.scenarios must be a non-empty array" });

  try {
    const env = {
      ...process.env,
      ADMIN_SCENARIOS: scenarios.join(","),
      TEST_MODE: testMode,
      ADMIN_EMAIL: config.ADMIN_EMAIL,
      ADMIN_PASSWORD: config.ADMIN_PASSWORD,
      ADMIN_AUTH_TOKEN: config.ADMIN_AUTH_TOKEN
    };

    const label = getTimestampLabel(scenarios);
    const logFile = getLogFilePath("admin", label, "scenario");
    const cmd = `k6 run "${adminMainFile}" > "${logFile}" 2>&1`;

    console.log(`🧪 Running admin scenario test for: ${scenarios.join(", ")}`);
    console.log(`📝 Executing: ${cmd}`);

    exec(cmd, { env }, (err) => {
      if (err) return res.status(500).json({ error: "admin k6 run failed", details: err.message });
      res.json({
        success: true,
        message: "Admin scenario test completed",
        logFile: path.basename(logFile, ".txt")
      });
    });
  } catch (error) {
    console.error("❌ Admin K6 API error:", error);
    res.status(500).json({ error: "Internal server error", details: error.message });
  }
});








app.post("/api/create-users", (req, res) => {
  const count = parseInt(req.query.count) || 5;

  const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
  const logFile = path.join(__dirname, "logs", `createUser-${count}.log`);
  const scriptPath = path.join(__dirname, "scripts", "create-users.dynamic.js");
  const cmd = `k6 run ${scriptPath} --env NUM_USERS=${count} > "${logFile}" 2>&1`;

  exec(cmd, (err) => {
    if (err) {
      console.error("CreateUsers failed:", err);
      return res.status(500).json({ success: false, error: "User creation failed" });
    }
    res.json({ success: true, message: `Created ${count} users`, logFile: path.basename(logFile) });
  });
});

app.post("/api/extract-users", (req, res) => {
  const logFile = req.body.logFile;
  if (!logFile || !logFile.endsWith('.log')) {
    return res.status(400).json({ success: false, error: "Invalid or missing logFile" });
  }

  const logPath = path.join(__dirname, "logs", logFile);
  const csvPath = path.join(__dirname, "data", "users.csv");
  const scriptPath = path.join(__dirname, "scripts", "extract-users.ps1");

  if (!fs.existsSync(logPath)) {
    return res.status(404).json({ success: false, error: "Log file not found" });
  }

  const psCommand = `powershell -ExecutionPolicy Bypass -File "${scriptPath}" -logPath "${logPath}" -csvPath "${csvPath}"`;

  exec(psCommand, (error, stdout, stderr) => {
    if (error) {
      console.error("PowerShell Error:", error);
      return res.status(500).json({ success: false, error: "Failed to extract users" });
    }

    console.log("PowerShell Output:", stdout);
    res.json({ success: true, message: "CSV created successfully", csvPath: "data/users.csv" });
  });
});

app.get("/api/users", (req, res) => {
  const csvPath = path.join(__dirname, "data", "users.csv"); // or users_with_tokens.csv

  if (!fs.existsSync(csvPath)) {
    return res.status(404).json({ success: false, error: "CSV file not found" });
  }

  fs.readFile(csvPath, "utf8", (err, data) => {
    if (err) {
      console.error("Failed to read CSV:", err);
      return res.status(500).json({ success: false, error: "Unable to read user CSV" });
    }

    const lines = data.trim().split("\n");
    const headers = lines[0].replace(/"/g, "").split(",");

    const users = lines.slice(1).map(line => {
      const values = line.replace(/"/g, "").split(",");
      const obj = {};
      headers.forEach((h, i) => {
        obj[h] = values[i];
      });
      return obj;
    });

    res.json({ success: true, count: users.length, users });
  });
});


app.post("/api/enable-wallets", (req, res) => {
  const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
  const logFile = path.join(__dirname, "logs",  `enableWallets-${timestamp}.log`);
  const scriptPath = path.join(__dirname, "scripts", "enable-wallets-from-csv.js");

  const csvPath = path.join(__dirname, "data", "users.csv");
  if (!fs.existsSync(csvPath)) {
    return res.status(404).json({ success: false, error: "users.csv not found" });
  }

  const numUsers = fs.readFileSync(csvPath, 'utf8')
    .split('\n')
    .filter(line => line.trim() && !line.includes('user_id')) // skip header and blanks
    .length;

  const cmd = `k6 run "${scriptPath}" --vus ${numUsers} --iterations ${numUsers} > "${logFile}" 2>&1`;

  exec(cmd, (err) => {
    if (err) {
      console.error("Enable Wallets failed:", err);
      return res.status(500).json({ success: false, error: "Wallet enabling failed" });
    }

    res.json({
      success: true,
      message: `Wallet enablement completed for ${numUsers} users`,
      logFile: path.basename(logFile),
    });
  });
});

// -----------------------------------
// User Token Management APIs
// -----------------------------------

// Login all users from CSV and generate tokens using K6
app.post('/api/users/login-csv', (req, res) => {
  try {
    console.log('🚀 Starting K6 CSV login process...');

    const loginScript = path.join(__dirname, 'scripts', 'loginWithCsv.js');
    const logFile = path.join(__dirname, 'login_output.log');

    if (!fs.existsSync(loginScript)) {
      return res.status(500).json({
        error: 'Login script not found',
        path: loginScript
      });
    }

    // Check if users.csv exists
    const usersCSV = path.join(__dirname, 'data', 'users.csv');
    if (!fs.existsSync(usersCSV)) {
      return res.status(400).json({
        error: 'users.csv not found. Please create the input CSV file first.',
        path: usersCSV
      });
    }

    // Run K6 script and capture output to log file
    const k6Command = `k6 run "${loginScript}" > "${logFile}" 2>&1`;
    console.log(`📝 Executing: ${k6Command}`);

    exec(k6Command, (error, stdout, stderr) => {
      if (error) {
        console.error('❌ K6 login script error:', error);
        return res.status(500).json({
          error: 'K6 login script execution failed',
          details: error.message,
          stderr: stderr
        });
      }

      console.log('✅ K6 login script completed');

      // Check if log file was created
      let summary = null;
      if (fs.existsSync(logFile)) {
        try {
          const logContent = fs.readFileSync(logFile, 'utf8');
          const tokenLines = logContent.split('\n').filter(line =>
            line.includes('Token fetched for') || line.includes('Login failed for')
          );

          summary = {
            logFile: logFile,
            totalAttempts: tokenLines.length,
            successful: tokenLines.filter(line => line.includes('Token fetched for')).length,
            failed: tokenLines.filter(line => line.includes('Login failed for')).length
          };
        } catch (readError) {
          console.warn('Could not parse log summary:', readError.message);
        }
      }

      res.json({
        success: true,
        message: 'K6 CSV login process completed',
        summary,
        logFile: logFile,
        nextStep: 'Run extract-tokens to generate CSV file'
      });
    });

  } catch (error) {
    console.error('❌ API error:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
});

// Extract tokens using PowerShell script and create JSON files
app.post('/api/users/extract-tokens', (req, res) => {
  try {
    console.log('🔍 Starting PowerShell token extraction...');

    const extractScript = path.join(__dirname, 'scripts', 'extractTokens.js');
    const logFile = path.join(__dirname, 'login_output.log');

    if (!fs.existsSync(extractScript)) {
      return res.status(500).json({
        error: 'Extract tokens script not found',
        path: extractScript
      });
    }

    if (!fs.existsSync(logFile)) {
      return res.status(400).json({
        error: 'Login log file not found. Run login-csv first.',
        logFile: logFile
      });
    }

    // Run the Node.js script that calls PowerShell
    exec(`node "${extractScript}"`, { cwd: path.join(__dirname, 'scripts') }, (error, stdout, stderr) => {
      if (error) {
        console.error('❌ Extract script error:', error);
        return res.status(500).json({
          error: 'Extract script execution failed',
          details: error.message,
          stderr: stderr
        });
      }

      console.log('✅ Extract script completed');

      const csvPath = path.join(__dirname, 'data', 'users_with_tokens.csv');
      const tokensPath = path.join(__dirname, 'data', 'tokens.json');
      const summaryPath = path.join(__dirname, 'data', 'users_summary.json');

      let tokensData = null;
      let summaryData = null;
      let csvSummary = null;

      try {
        // Check if CSV was generated
        if (fs.existsSync(csvPath)) {
          const csvContent = fs.readFileSync(csvPath, 'utf8');
          const lines = csvContent.split('\n').filter(line => line.trim());
          const users = lines.slice(1); // Skip header

          csvSummary = {
            total: users.length,
            withTokens: users.filter(line => {
              const parts = line.split(',');
              return parts.length >= 3 && parts[2] && parts[2].trim() !== '';
            }).length
          };
        }

        if (fs.existsSync(tokensPath)) {
          tokensData = JSON.parse(fs.readFileSync(tokensPath, 'utf8'));
        }

        if (fs.existsSync(summaryPath)) {
          summaryData = JSON.parse(fs.readFileSync(summaryPath, 'utf8'));
        }
      } catch (readError) {
        console.warn('Could not read result files:', readError.message);
      }

      res.json({
        success: true,
        message: 'PowerShell token extraction completed',
      });
    });

  } catch (error) {
    console.error('❌ API error:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
});

// Get all user tokens data
app.get('/api/users/tokens', (req, res) => {
  try {
    const tokensPath = path.join(__dirname, 'data', 'tokens.json');

    if (!fs.existsSync(tokensPath)) {
      return res.status(404).json({
        error: 'Tokens file not found. Run extract-tokens first.',
        path: tokensPath
      });
    }

    const tokensData = JSON.parse(fs.readFileSync(tokensPath, 'utf8'));

    res.json({
      success: true,
      data: tokensData
    });

  } catch (error) {
    console.error('❌ Error reading tokens:', error);
    res.status(500).json({
      error: 'Failed to read tokens file',
      details: error.message
    });
  }
});

// Get users summary data
app.get('/api/users/summary', (req, res) => {
  try {
    const summaryPath = path.join(__dirname, 'data', 'users_summary.json');

    if (!fs.existsSync(summaryPath)) {
      return res.status(404).json({
        error: 'Summary file not found. Run extract-tokens first.',
        path: summaryPath
      });
    }

    const summaryData = JSON.parse(fs.readFileSync(summaryPath, 'utf8'));

    res.json({
      success: true,
      data: summaryData
    });

  } catch (error) {
    console.error('❌ Error reading summary:', error);
    res.status(500).json({
      error: 'Failed to read summary file',
      details: error.message
    });
  }
});

// Check status of CSV files and data
app.get('/api/users/csv-status', (req, res) => {
  try {
    const csvPath = path.join(__dirname, 'data', 'users_with_tokens.csv');
    const tokensPath = path.join(__dirname, 'data', 'tokens.json');
    const summaryPath = path.join(__dirname, 'data', 'users_summary.json');

    const status = {
      csv: {
        exists: fs.existsSync(csvPath),
        path: csvPath,
        size: fs.existsSync(csvPath) ? fs.statSync(csvPath).size : 0
      },
      tokens: {
        exists: fs.existsSync(tokensPath),
        path: tokensPath,
        size: fs.existsSync(tokensPath) ? fs.statSync(tokensPath).size : 0
      },
      summary: {
        exists: fs.existsSync(summaryPath),
        path: summaryPath,
        size: fs.existsSync(summaryPath) ? fs.statSync(summaryPath).size : 0
      }
    };

    // If CSV exists, get basic stats
    if (status.csv.exists) {
      try {
        const csvContent = fs.readFileSync(csvPath, 'utf8');
        const lines = csvContent.split('\n').filter(line => line.trim());
        const users = lines.slice(1); // Skip header

        status.csv.userCount = users.length;
        status.csv.successfulLogins = users.filter(line => line.includes('success')).length;
        status.csv.failedLogins = users.filter(line => line.includes('failed')).length;
      } catch (parseError) {
        status.csv.parseError = parseError.message;
      }
    }

    res.json({
      success: true,
      status
    });

  } catch (error) {
    console.error('❌ Error checking status:', error);
    res.status(500).json({
      error: 'Failed to check file status',
      details: error.message
    });
  }
});





// -----------------------------------
app.get('/api/ping', (req, res) => {
  res.status(200).json({ message: 'pong', ts: Date.now() });
});

// Start server
app.listen(PORT, () => {
  console.log(`Server running on http://localhost:${PORT}`);
});
