
         /\      Grafana   /‾‾/  
    /\  /  \     |\  __   /  /   
   /  \/    \    | |/ /  /   ‾‾\ 
  /          \   |   (  |  (‾)  |
 / __________ \  |_|\_\  \_____/ 

time="2025-06-16T21:07:54+05:30" level=info msg="✅ Loaded 5 users from CSV" source=console
     execution: local
        script: C:\Users\<USER>\Documents\k6\k6\scenarios\main.test.js
        output: -

     scenarios: (100.00%) 1 scenario, 1 max VUs, 10m30s max duration (incl. graceful stop):
              * default: 1 iterations shared among 1 VUs (maxDuration: 10m0s, gracefulStop: 30s)

time="2025-06-16T21:07:54+05:30" level=info msg="🚀 Running USER tests with mode: scenario" source=console
time="2025-06-16T21:07:54+05:30" level=info msg="📋 Scenarios selected: buyGoodLimitOrderFlow, buyGoodMarketScenario, sellGoodLimitOrderFlow, sellGoodMarketOrder, buyGoodStopLossOrderFlow, sellGoodStopLossOrderFlow, goodWatchlist, goodUserTransactions, goodChangePassword, testGoodLogins, testGoodRegister, buyBadLimitOrderFlow, buyBadMarketScenario, sellBadLimitOrderFlow, sellBadMarketOrder, buyBadStopLossOrderFlow, sellBadStopLossOrderFlow, badWatchlist, testBadRegister, badUserTransactions, badChangePassword, testBadLogins, testGetInstruments, testMarketOrders, testOtherCases" source=console
time="2025-06-16T21:07:55+05:30" level=error msg="Error in /api/auth/login (existing user): Response: {\n  \"message\": \"Invalid credentials\",\n  \"status\": \"fail\"\n}" source=console

running (00m01.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m01.0s/10m0s  0/1 shared iters

running (00m02.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m02.0s/10m0s  0/1 shared iters

running (00m03.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m03.0s/10m0s  0/1 shared iters
time="2025-06-16T21:07:58+05:30" level=error msg="Register with valid overseeUserID fails: 400 → {\"message\":\"password must contain at least one uppercase letter\",\"status\":\"fail\"}" source=console
time="2025-06-16T21:07:58+05:30" level=error msg="Register without overseeUserID fails: 400 → {\"message\":\"password must contain at least one uppercase letter\",\"status\":\"fail\"}" source=console

running (00m04.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m04.0s/10m0s  0/1 shared iters

running (00m05.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m05.0s/10m0s  0/1 shared iters

running (00m06.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m06.0s/10m0s  0/1 shared iters
time="2025-06-16T21:08:01+05:30" level=info msg="✅ Using static auth for scenario testing (VU: 1)" source=console

running (00m07.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m07.0s/10m0s  0/1 shared iters
time="2025-06-16T21:08:02+05:30" level=error msg="buyLimitOrder: Invalid instrument prices → LastPrice: 6060, LowerCkt: 0" source=console
time="2025-06-16T21:08:02+05:30" level=info msg="✅ Using static auth for scenario testing (VU: 1)" source=console

running (00m08.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m08.0s/10m0s  0/1 shared iters

running (00m09.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m09.0s/10m0s  0/1 shared iters

running (00m10.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m10.0s/10m0s  0/1 shared iters
time="2025-06-16T21:08:04+05:30" level=info msg="✅ Using static auth for scenario testing (VU: 1)" source=console
time="2025-06-16T21:08:05+05:30" level=info msg="🎯 Using instrumentId: 1120250618445825, LastPrice: 6070, UpperCkt: 0" source=console
time="2025-06-16T21:08:05+05:30" level=error msg="buyStopLossOrder Skipping: Invalid instrument price data → LastPrice: 6070, UpperCkt: 0" source=console
time="2025-06-16T21:08:05+05:30" level=info msg="✅ Using static auth for scenario testing (VU: 1)" source=console

running (00m11.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m11.0s/10m0s  0/1 shared iters
time="2025-06-16T21:08:05+05:30" level=error msg="📤 buyStopLossOrder_STOPLOSS → 400 on too low triggerPrice Payload: {\n  \"instrumentId\": 1120250618445825,\n  \"exchange\": \"MCX\",\n  \"quantity\": 1,\n  \"OrderType\": \"STOPLOSS\",\n  \"buy\": true,\n  \"triggerPrice\": 0.06\n}" source=console
time="2025-06-16T21:08:05+05:30" level=error msg="📥 buyStopLossOrder_STOPLOSS → 400 on too low triggerPrice Response: 200 {\"message\":\"STOPLOSS order placed. Funds blocked: 0.000600\",\"status\":\"success\",\"time\":\"2025-06-16T15:38:06.744199341Z\"}" source=console
time="2025-06-16T21:08:05+05:30" level=error msg="📤 buyStopLossOrder_STOPLOSS → 400 on triggerPrice above upper circuit Payload: {\n  \"instrumentId\": 1120250618445825,\n  \"exchange\": \"MCX\",\n  \"quantity\": 1,\n  \"OrderType\": \"STOPLOSS\",\n  \"buy\": true,\n  \"triggerPrice\": 10\n}" source=console
time="2025-06-16T21:08:05+05:30" level=error msg="📥 buyStopLossOrder_STOPLOSS → 400 on triggerPrice above upper circuit Response: 200 {\"message\":\"STOPLOSS order placed. Funds blocked: 0.100000\",\"status\":\"success\",\"time\":\"2025-06-16T15:38:06.993899087Z\"}" source=console

running (00m12.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m12.0s/10m0s  0/1 shared iters
time="2025-06-16T21:08:06+05:30" level=info msg="✅ Using static auth for scenario testing (VU: 1)" source=console
time="2025-06-16T21:08:07+05:30" level=info msg="[sellLimitOrder] 💰 Net Balance BEFORE: 69491.94369999999" source=console
time="2025-06-16T21:08:07+05:30" level=error msg="sellLimitOrder Skipping: Invalid instrument price data → LastPrice: 6062, LowerCkt: 0" source=console
time="2025-06-16T21:08:07+05:30" level=info msg="✅ Using static auth for scenario testing (VU: 1)" source=console

running (00m13.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m13.0s/10m0s  0/1 shared iters

running (00m14.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m14.0s/10m0s  0/1 shared iters
time="2025-06-16T21:08:09+05:30" level=info msg="✅ Using static auth for scenario testing (VU: 1)" source=console
time="2025-06-16T21:08:09+05:30" level=error msg="sellStopLossOrder: Invalid instrument data → LastPrice: 6055, UpperCkt: 0" source=console
time="2025-06-16T21:08:09+05:30" level=info msg="✅ Using static auth for scenario testing (VU: 1)" source=console

running (00m15.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m15.0s/10m0s  0/1 shared iters

running (00m16.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m16.0s/10m0s  0/1 shared iters

running (00m17.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m17.0s/10m0s  0/1 shared iters
time="2025-06-16T21:08:11+05:30" level=info msg="✅ buyGoodMarketScenario: Using user: IU-ANOOUC (VU: 1)" source=console

running (00m18.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m18.0s/10m0s  0/1 shared iters

running (00m19.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m19.0s/10m0s  0/1 shared iters

running (00m20.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m20.0s/10m0s  0/1 shared iters

running (00m21.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m21.0s/10m0s  0/1 shared iters

running (00m22.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m22.0s/10m0s  0/1 shared iters

running (00m23.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m23.0s/10m0s  0/1 shared iters
time="2025-06-16T21:08:17+05:30" level=info msg="✅ Using static auth for scenario testing (VU: 1)" source=console

running (00m24.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m24.0s/10m0s  0/1 shared iters

running (00m25.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m25.0s/10m0s  0/1 shared iters
time="2025-06-16T21:08:19+05:30" level=error msg="CHECK FAILED: sellMarketOrder_netBalance increase after SELL\nExpected: netAfter > netBefore\nActual: netBefore = 69431.4037, netAfter = 69311.7237" source=console

running (00m26.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m26.0s/10m0s  0/1 shared iters
time="2025-06-16T21:08:20+05:30" level=info msg="✅ Using static auth for scenario testing (VU: 1)" source=console

running (00m27.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m27.0s/10m0s  0/1 shared iters

running (00m28.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m28.0s/10m0s  0/1 shared iters
time="2025-06-16T21:08:22+05:30" level=info msg="✅ Using static auth for scenario testing (VU: 1)" source=console

running (00m29.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m29.0s/10m0s  0/1 shared iters

running (00m30.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m30.0s/10m0s  0/1 shared iters

running (00m31.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m31.0s/10m0s  0/1 shared iters

running (00m32.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m32.0s/10m0s  0/1 shared iters
time="2025-06-16T21:08:27+05:30" level=info msg="✅ Using static auth for scenario testing (VU: 1)" source=console

running (00m33.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m33.0s/10m0s  0/1 shared iters
time="2025-06-16T21:08:27+05:30" level=error msg="addToWatchlist_invalid instrumentId Payload: {\n  \"instrumentId\": 999999999999,\n  \"exchange\": \"MCX\"\n}" source=console
time="2025-06-16T21:08:27+05:30" level=error msg="addToWatchlist_invalid instrumentId Response: 200 \"success\"" source=console

running (00m34.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m34.0s/10m0s  0/1 shared iters

running (00m35.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m35.0s/10m0s  0/1 shared iters

running (00m36.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m36.0s/10m0s  0/1 shared iters

running (00m37.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m37.0s/10m0s  0/1 shared iters

running (00m38.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m38.0s/10m0s  0/1 shared iters
time="2025-06-16T21:08:32+05:30" level=info msg="✅ Using static auth for scenario testing (VU: 1)" source=console
time="2025-06-16T21:08:33+05:30" level=info msg="✅ Using static auth for scenario testing (VU: 1)" source=console

running (00m39.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m39.0s/10m0s  0/1 shared iters
time="2025-06-16T21:08:33+05:30" level=info msg="✅ Using static auth for scenario testing (VU: 1)" source=console
time="2025-06-16T21:08:33+05:30" level=error msg="updatePassword POST /api/user/changePassword failed: 400 {\"message\":\"Current Password Incorrect\",\"status\":\"fail\"}" source=console

running (00m40.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m40.0s/10m0s  0/1 shared iters
time="2025-06-16T21:08:34+05:30" level=info msg="✅ Using static auth for scenario testing (VU: 1)" source=console

running (00m41.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m41.0s/10m0s  0/1 shared iters

running (00m42.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m42.0s/10m0s  0/1 shared iters


  █ TOTAL RESULTS 

    checks_total.......................: 144    3.372684/s
    checks_succeeded...................: 89.58% 129 out of 144
    checks_failed......................: 10.41% 15 out of 144

    ✗ login : valid user → 200
      ↳  0% — ✓ 0 / ✗ 1
    ✗ login : status = success
      ↳  50% — ✓ 1 / ✗ 1
    ✗ login : password_reset = false
      ↳  0% — ✓ 0 / ✗ 1
    ✓ login : valid user (reset) → 200
    ✓ login : password_reset = true
    ✗ login : wrong password → 400 with fail
      ↳  0% — ✓ 0 / ✗ 1
    ✗ login : invalid user → 400 with fail
      ↳  0% — ✓ 0 / ✗ 1
    ✓ login : missing password → 400+
    ✓ login : missing identifier → 400+
    ✓ login : malformed JSON → 400+
    ✓ login : no Content-Type → 200
    ✗ Register : with overseeUserID full payload → 201
      ↳  0% — ✓ 0 / ✗ 1
    ✗ Register : with overseeUserID  status = success
      ↳  0% — ✓ 0 / ✗ 1
    ✗ Register : with only email and password → 201
      ↳  0% — ✓ 0 / ✗ 1
    ✗ Register : with only email and password status = success
      ↳  0% — ✓ 0 / ✗ 1
    ✓ Register : duplicate email → fail
    ✓ Register : missing password → 400
    ✓ Register : missing email → 400
    ✓ Register : invalid email format → 400+
    ✓ Register : empty request body → 400
    ✓ no Content-Type → 400+
    ✓ buyLimitOrder GET wallet before → 200
    ✓ buyLimitOrder GET wallet before → has wallets
    ✓ buyLimitOrder POST getInstruments → 200
    ✓ buyLimitOrder POST getInstruments → has instruments
    ✓ buyLimitOrder POST makeOrder → missing triggerPrice → 400+
    ✓ buyLimitOrder POST makeOrder → invalid instrumentId → 400+
    ✓ buyLimitOrder POST makeOrder → invalid quantity → 400+
    ✓ buyLimitOrder POST makeOrder → malformed JSON → 400+
    ✓ buyLimitOrder POST makeOrder → no auth → 401+
    ✓ buyLimitOrder POST makeOrder → bad token → 401+
    ✓ buyLimitOrder POST getInstruments → no auth → 401+
    ✓ buyLimitOrder POST getInstruments → bad token → 401+
    ✓ wallet before → 200
    ✓ buyStopLossOrder_getInstruments → 200
    ✓ buyStopLossOrder_STOPLOSS → 400 on missing triggerPrice
    ✓ buyStopLossOrder_STOPLOSS → fail on invalid instrumentId
    ✗ buyStopLossOrder_STOPLOSS → 400 on too low triggerPrice
      ↳  0% — ✓ 0 / ✗ 1
    ✗ buyStopLossOrder_STOPLOSS → 400 on triggerPrice above upper circuit
      ↳  0% — ✓ 0 / ✗ 1
    ✓ sellLimitOrder_wallet before → 200
    ✓ sellLimitOrder_getInstruments → 200
    ✓ sellLimitOrder_makeOrder → 400/422 on missing triggerPrice
    ✓ sellLimitOrder_makeOrder → fail on invalid instrumentId
    ✓ sellLimitOrder_makeOrder → 400 on too low triggerPrice
    ✓ sellStopLossOrder GET wallet before → 200
    ✓ sellStopLossOrder GET wallet before → has wallets
    ✓ sellStopLossOrder POST getInstruments → 200
    ✓ sellStopLossOrder POST getInstruments → has instruments
    ✓ sellStopLossOrder POST makeOrder → missing triggerPrice → 400+
    ✓ sellStopLossOrder POST makeOrder → invalid instrumentId → 400+
    ✓ sellStopLossOrder POST makeOrder → invalid quantity → 400+
    ✓ sellStopLossOrder POST makeOrder → malformed JSON → 400+
    ✓ sellStopLossOrder POST makeOrder → no auth → 401+
    ✓ sellStopLossOrder POST makeOrder → bad token → 401+
    ✓ sellStopLossOrder POST getInstruments → no auth → 401+
    ✓ sellStopLossOrder POST getInstruments → bad token → 401+
    ✓ buyGoodMarketScenario GET wallet before → 200
    ✓ buyGoodMarketScenario GET wallet before → has wallets
    ✓ buyGoodMarketScenario POST getInstruments → 200
    ✓ buyGoodMarketScenario POST getInstruments → has instruments
    ✓ buyGoodMarketScenario POST makeOrder → 200
    ✓ buyGoodMarketScenario POST makeOrder → success
    ✓ buyGoodMarketScenario GET wallet after → 200
    ✓ buyGoodMarketScenario GET wallet after → has wallets
    ✓ buyGoodMarketScenario → net_balance reduced after order
    ✓ buyGoodMarketScenario POST getPortfolio → 200
    ✓ buyGoodMarketScenario POST getPortfolio → has portfolios
    ✓ buyGoodMarketScenario → portfolio includes instrument
    ✓ buyBadMarketScenario POST getInstruments → invalid token → 401+
    ✓ buyBadMarketScenario POST getInstruments → no auth → 401+
    ✓ buyBadMarketScenario POST makeOrder → invalid token → 401+
    ✓ buyBadMarketScenario POST makeOrder → no auth → 401+
    ✓ buyBadMarketScenario POST makeOrder → missing instrumentId → 400+
    ✓ buyBadMarketScenario POST makeOrder → invalid instrumentId → 400+
    ✓ buyBadMarketScenario POST makeOrder → invalid quantity → 400+
    ✓ buyBadMarketScenario POST makeOrder → malformed JSON → 400+
    ✓ buyBadMarketScenario POST getInstruments → malformed JSON → 400+
    ✓ buyBadMarketScenario POST getPortfolio → invalid token → 401+
    ✓ buyBadMarketScenario POST getPortfolio → no auth → 401+
    ✓ buyBadMarketScenario POST getPortfolio → malformed JSON → 400+
    ✓ sellMarketOrder_wallet before → 200
    ✓ sellMarketOrder_getInstruments → 200
    ✓ sellMarketOrder_makeOrder → 200
    ✓ sellMarketOrder_makeOrder → success
    ✓ sellMarketOrder_wallet after → 200
    ✗ sellMarketOrder_netBalance increase after SELL
      ↳  0% — ✓ 0 / ✗ 1
    ✓ sellMarketOrder_getPortfolio → 200
    ✓ sellMarketOrder_getPortfolio → includes instrument
    ✓ getInstruments → 401 on invalid token
    ✓ sellMarketOrder_makeOrder → 400/422 on missing instrumentId
    ✓ sellMarketOrder_makeOrder → fails with invalid instrumentId
    ✓ sellMarketOrder_getInstruments → 403 on missing token
    ✓ addToWatchlist_getInstruments → 200
    ✓ addToWatchlist_getInstruments → has instruments
    ✓ addToWatchlist → 200
    ✓ addToWatchlist → success
    ✓ addToWatchlist_watchlist → 200
    ✓ addToWatchlist_watchlist checking addded or not→ includes instrument
    ✓ removeFromWatchlist → 200
    ✓ removeFromWatchlist → success
    ✓ removeFromWatchlist_watchlist (after remove) → 200
    ✓ removeFromWatchlist_watchlist → no longer includes instrument
    ✓ addToWatchlist_missing instrumentId → 400+
    ✓ addToWatchlist_missing exchange → 400+
    ✗ addToWatchlist_invalid instrumentId → 400+
      ↳  0% — ✓ 0 / ✗ 1
    ✓ addToWatchlist_invalid token → 401+
    ✓ addToWatchlist_no token → 403+
    ✓ addToWatchlist_malformed JSON (add) → 400+
    ✓ watchlist → missing exchange → 400+
    ✓ watchlist → bad token → 401+
    ✓ watchlist → no token → 403+
    ✓ watchlist → malformed body → 400+
    ✓ removeFromWatchlist → missing instrumentId → 400+
    ✓ removeFromWatchlist → missing exchange → 400+
    ✓ removeFromWatchlist → bad token → 401+
    ✓ removeFromWatchlist → no token → 403+
    ✓ removeFromWatchlist → malformed body → 400+
    ✓ invalid token → 401
    ✓ no token → 403
    ✓ invalid exchange → 400
    ✓ invalid request body → 400
    ✓ invalid order type → 400
    ✓ invalid instrumentId → 400
    ✓ quantity > per order limit → 400
    ✓ quantity > total allowed limit → 400
    ✓ insufficient balance → 400
    ✓ userTransactions POST /api/user/transactions → 200
    ✓ userTransactions POST /api/user/transactions → response is array
    ✓ userTransactions POST /api/user/transactions → transactions valid
    ✓ missing currency → 400 or valid empty
    ✗ updatePassword POST /api/user/changePassword → 200
      ↳  0% — ✓ 0 / ✗ 1
    ✗ updatePassword POST /api/user/changePassword → success message
      ↳  0% — ✓ 0 / ✗ 1
    ✓ updatePassword POST /api/user/changePassword → wrong current password → 400+
    ✓ updatePassword POST /api/user/changePassword → same password → 400+
    ✓ updatePassword POST /api/user/changePassword → missing newPassword → 400+
    ✓ updatePassword POST /api/user/changePassword → missing currentPassword → 400+
    ✓ updatePassword POST /api/user/changePassword → malformed JSON → 400+
    ✓ updatePassword POST /api/user/changePassword → no auth → 401+
    ✓ updatePassword POST /api/user/changePassword → bad token → 401+

    HTTP
    http_req_duration.......................................................: avg=191.24ms min=167.51ms med=172.89ms max=568.48ms p(90)=216.57ms p(95)=251.78ms
      { expected_response:true }............................................: avg=209.01ms min=171.16ms med=185.18ms max=539.95ms p(90)=223.63ms p(95)=364.9ms 
    http_req_failed.........................................................: 73.72% 87 out of 118
    http_reqs...............................................................: 118    2.763728/s

    EXECUTION
    iteration_duration......................................................: avg=42.69s   min=42.69s   med=42.69s   max=42.69s   p(90)=42.69s   p(95)=42.69s  
    iterations..............................................................: 1      0.023421/s
    vus.....................................................................: 1      min=1         max=1
    vus_max.................................................................: 1      min=1         max=1

    NETWORK
    data_received...........................................................: 78 kB  1.8 kB/s
    data_sent...............................................................: 17 kB  388 B/s




running (00m42.7s), 0/1 VUs, 1 complete and 0 interrupted iterations
default ✓ [ 100% ] 1 VUs  00m42.7s/10m0s  1/1 shared iters
