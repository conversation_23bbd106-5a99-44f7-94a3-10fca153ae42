import http from 'k6/http';
import { group, check } from 'k6';
import {
  ADMIN_BASE,
  ADMIN_AUTH_TOKEN,
  USER_ID,
  OVERSEE_USER_ID,
  EXCHANGE
} from '../config/config.js';

const validHeaders = {
  'Content-Type': 'application/json',
  Authorization: ADMIN_AUTH_TOKEN,
};

const badHeaders = {
  'Content-Type': 'application/json',
  Authorization: 'Bearer invalid-token',
};

const noAuthHeaders = {
  'Content-Type': 'application/json',
};

export const options = {
  vus: 1,
  iterations: 1,
};

export default function () {
  goodWalletEnableAndAddingBalanceScenario();
  badWalletEnableAndAddingBalanceScenario();
}

// ✅ POSITIVE FLOW
export function goodWalletEnableAndAddingBalanceScenario() {
  group('walletEnableAndAddingBalance Wallet Flow: Enable → Verify → Apply Balance → Verify', () => {
    const walletList = {
      NSE: true,
      MCX: true,
      CRYPTO: true,
      NASDAQ: true,
    };

    const applyAmount = 10000;

    // 1. Enable wallets
    const enablePayload = JSON.stringify({ user_id: USER_ID, list: walletList });
    const enableRes = http.post(`${ADMIN_BASE}/user/wallets`, enablePayload, { headers: validHeaders });
    const enableOk = check(enableRes, {
      'walletEnableAndAddingBalance POST /user/wallets → 200': (r) => r.status === 200,
      'walletEnableAndAddingBalance Enable Wallets → Message OK': (r) => r.json().message === 'Wallet values updated successfully',
    });
    if (!enableOk) {
      console.error('walletEnableAndAddingBalance Enable Wallets Failed:', enableRes.status, enableRes.body);
      return;
    }

    // 2. Verify wallets
    const getRes = http.get(`${ADMIN_BASE}/user/wallets/${USER_ID}`, { headers: validHeaders });
    const getOk = check(getRes, {
      'walletEnableAndAddingBalance GET /user/wallets/:user_id → 200': (r) => r.status === 200,
      'walletEnableAndAddingBalance Wallet Flags Verified': (r) =>
        r.json().NSE && r.json().MCX && r.json().CRYPTO && r.json().NASDAQ,
    });
    if (!getOk) {
      console.error('walletEnableAndAddingBalance Wallet Verification Failed:', getRes.status, getRes.body);
      return;
    }

    // 3. Check agent balance
    const agentWalletRes = http.get(`${ADMIN_BASE}/wallets/balance/${OVERSEE_USER_ID}`, { headers: validHeaders });
    const agentWalletOk = check(agentWalletRes, { 'walletEnableAndAddingBalance GET /wallets/balance/oversee → 200': r => r.status === 200 });
    if (!agentWalletOk) {
      console.error('walletEnableAndAddingBalance Agent Wallet Check Failed:', agentWalletRes.status, agentWalletRes.body);
      return;
    }

    const available = agentWalletRes.json()?.wallets?.find(w => w.exchange === EXCHANGE)?.balance || 0;
    if (available < applyAmount) {
      console.error(`walletEnableAndAddingBalance Insufficient balance. Available: ${available}, Required: ${applyAmount}`);
      return;
    }

    // 4. Apply balance
    const applyPayload = JSON.stringify({
      user_id: USER_ID,
      exchange: EXCHANGE,
      balance: `${applyAmount}`,
      oversee_id: OVERSEE_USER_ID,
      updated_by: OVERSEE_USER_ID,
    });

    const applyRes = http.post(`${ADMIN_BASE}/user/applyBalance`, applyPayload, { headers: validHeaders });
    const applyOk = check(applyRes, {
      'walletEnableAndAddingBalance POST /user/applyBalance → 200': r => r.status === 200,
      'walletEnableAndAddingBalance Apply Balance → success msg': r => r.json().message?.includes('balances updated'),
    });
    if (!applyOk) {
      console.error('walletEnableAndAddingBalance Apply Balance Failed:', applyRes.status, applyRes.body);
      return;
    }

    // 5. Verify user wallet balance
    const userWalletRes = http.get(`${ADMIN_BASE}/wallets/balance/${USER_ID}`, { headers: validHeaders });
    const userWalletOk = check(userWalletRes, {
      'walletEnableAndAddingBalance GET /wallets/balance/user → 200': r => r.status === 200,
      'walletEnableAndAddingBalance Balance Updated': (r) => {
        const bal = r.json()?.wallets?.find(w => w.exchange === EXCHANGE)?.balance || 0;
        return bal >= applyAmount;
      },
    });
    if (!userWalletOk) {
      console.error('walletEnableAndAddingBalance User Wallet Balance Check Failed:', userWalletRes.status, userWalletRes.body);
    }
  });
}

// ❌ NEGATIVE SCENARIOS
export function badWalletEnableAndAddingBalanceScenario() {
  group('walletEnableAndAddingBalance Wallet Flow: Negative Cases', () => {
    const dummyId = 'FAKE-USER';
    const dummyPayload = JSON.stringify({
      user_id: dummyId,
      list: { NSE: true },
    });

    // --- Wallet enablement failures ---
    const malformedJson = '{"user_id": "IU-XXX", list: true'; // bad format

    const tests = [
      {
        desc: 'walletEnableAndAddingBalance POST /user/wallets → missing user_id',
        url: '/user/wallets',
        payload: { list: { NSE: true } },
        headers: validHeaders,
      },
      {
        desc: 'walletEnableAndAddingBalance POST /user/wallets → missing list',
        url: '/user/wallets',
        payload: { user_id: USER_ID },
        headers: validHeaders,
      },
      {
        desc: 'walletEnableAndAddingBalance POST /user/wallets → invalid user_id',
        url: '/user/wallets',
        payload: { user_id: dummyId, list: { NSE: true } },
        headers: validHeaders,
      },
      {
        desc: 'walletEnableAndAddingBalance POST /user/wallets → bad token',
        url: '/user/wallets',
        payload: { user_id: USER_ID, list: { NSE: true } },
        headers: badHeaders,
      },
      {
        desc: 'walletEnableAndAddingBalance POST /user/wallets → no token',
        url: '/user/wallets',
        payload: { user_id: USER_ID, list: { NSE: true } },
        headers: noAuthHeaders,
      },
    ];

    for (const t of tests) {
      const res = http.post(`${ADMIN_BASE}${t.url}`, JSON.stringify(t.payload), { headers: t.headers });
      const ok = check(res, {
        [`${t.desc} → 400+`]: (r) => r.status >= 400,
      });
      if (!ok) console.error(`${t.desc} Failed:`, res.status, res.body);
    }

    // Malformed body test
    const malformedRes = http.post(`${ADMIN_BASE}/user/wallets`, malformedJson, { headers: validHeaders });
    const malformedOk = check(malformedRes, { 'walletEnableAndAddingBalance Malformed JSON → 400+': r => r.status >= 400 });
    if (!malformedOk) {
      console.error('walletEnableAndAddingBalance Malformed body → Unexpected result:', malformedRes.status, malformedRes.body);
    }

    // --- Apply balance with missing fields ---
    const applyFailRes = http.post(`${ADMIN_BASE}/user/applyBalance`, JSON.stringify({
      exchange: EXCHANGE,
      balance: '1000',
      updated_by: OVERSEE_USER_ID,
    }), { headers: validHeaders });

    const applyFailOk = check(applyFailRes, { 'walletEnableAndAddingBalance POST /applyBalance → missing user_id → 400+': r => r.status >= 400 });
    if (!applyFailOk) {
      console.error('walletEnableAndAddingBalance Apply Balance (missing user_id) Failed:', applyFailRes.status, applyFailRes.body);
    }

    const badApplyToken = http.post(`${ADMIN_BASE}/user/applyBalance`, JSON.stringify({
      user_id: USER_ID,
      exchange: EXCHANGE,
      balance: '1000',
      oversee_id: OVERSEE_USER_ID,
      updated_by: OVERSEE_USER_ID,
    }), { headers: badHeaders });

    const badApplyOk = check(badApplyToken, {
      'walletEnableAndAddingBalance Apply Balance → bad token → 401+': (r) => r.status >= 401,
    });

    if (!badApplyOk) {
      console.error('walletEnableAndAddingBalance Apply Balance (bad token) failed:', badApplyToken.status, badApplyToken.body);
    }
  });
}
