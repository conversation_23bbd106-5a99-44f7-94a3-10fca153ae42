const express = require('express');
const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const Papa = require('papaparse');

const router = express.Router();

// Paths
const DATA_DIR = path.join(__dirname, '../data');
const SCRIPTS_DIR = path.join(__dirname, '../scripts');
const CSV_PATH = path.join(DATA_DIR, 'users_with_tokens.csv');
const TOKENS_JSON_PATH = path.join(DATA_DIR, 'tokens.json');
const USERS_SUMMARY_PATH = path.join(DATA_DIR, 'users_summary.json');

/**
 * POST /api/users/login-csv
 * Login all users from CSV and generate tokens
 */
router.post('/login-csv', async (req, res) => {
  try {
    console.log('🚀 Starting CSV login process...');
    
    const loginScript = path.join(SCRIPTS_DIR, 'loginWithCsv.js');
    
    // Check if script exists
    if (!fs.existsSync(loginScript)) {
      return res.status(500).json({
        error: 'Login script not found',
        path: loginScript
      });
    }

    // Execute login script
    exec(`node "${loginScript}"`, { cwd: SCRIPTS_DIR }, (error, stdout, stderr) => {
      if (error) {
        console.error('❌ Login script error:', error);
        return res.status(500).json({
          error: 'Login script execution failed',
          details: error.message,
          stderr: stderr
        });
      }

      console.log('✅ Login script completed');
      
      // Try to read the results
      try {
        const csvExists = fs.existsSync(CSV_PATH);
        let summary = null;
        
        if (csvExists) {
          const csvContent = fs.readFileSync(CSV_PATH, 'utf8');
          const parsed = Papa.parse(csvContent, { header: true });
          const users = parsed.data.filter(u => u.user_id);
          
          summary = {
            total: users.length,
            successful: users.filter(u => u.login_status === 'success').length,
            failed: users.filter(u => u.login_status === 'failed').length,
            csvPath: CSV_PATH
          };
        }

        res.json({
          success: true,
          message: 'CSV login process completed',
          summary,
          stdout: stdout,
          csvGenerated: csvExists
        });
        
      } catch (readError) {
        res.json({
          success: true,
          message: 'CSV login process completed but could not read results',
          stdout: stdout,
          readError: readError.message
        });
      }
    });

  } catch (error) {
    console.error('❌ API error:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
});

/**
 * POST /api/users/extract-tokens
 * Extract tokens from CSV and create JSON files
 */
router.post('/extract-tokens', async (req, res) => {
  try {
    console.log('🔍 Starting token extraction...');
    
    const extractScript = path.join(SCRIPTS_DIR, 'extractTokens.js');
    
    // Check if script exists
    if (!fs.existsSync(extractScript)) {
      return res.status(500).json({
        error: 'Extract tokens script not found',
        path: extractScript
      });
    }

    // Check if CSV exists
    if (!fs.existsSync(CSV_PATH)) {
      return res.status(400).json({
        error: 'Users CSV file not found. Run login-csv first.',
        csvPath: CSV_PATH
      });
    }

    // Execute extract script
    exec(`node "${extractScript}"`, { cwd: SCRIPTS_DIR }, (error, stdout, stderr) => {
      if (error) {
        console.error('❌ Extract script error:', error);
        return res.status(500).json({
          error: 'Extract script execution failed',
          details: error.message,
          stderr: stderr
        });
      }

      console.log('✅ Extract script completed');
      
      // Try to read the results
      try {
        let tokensData = null;
        let summaryData = null;
        
        if (fs.existsSync(TOKENS_JSON_PATH)) {
          tokensData = JSON.parse(fs.readFileSync(TOKENS_JSON_PATH, 'utf8'));
        }
        
        if (fs.existsSync(USERS_SUMMARY_PATH)) {
          summaryData = JSON.parse(fs.readFileSync(USERS_SUMMARY_PATH, 'utf8'));
        }

        res.json({
          success: true,
          message: 'Token extraction completed',
          tokensData,
          summaryData,
          stdout: stdout,
          files: {
            tokens: fs.existsSync(TOKENS_JSON_PATH),
            summary: fs.existsSync(USERS_SUMMARY_PATH)
          }
        });
        
      } catch (readError) {
        res.json({
          success: true,
          message: 'Token extraction completed but could not read results',
          stdout: stdout,
          readError: readError.message
        });
      }
    });

  } catch (error) {
    console.error('❌ API error:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
});

/**
 * GET /api/users/tokens
 * Get all user tokens data
 */
router.get('/tokens', (req, res) => {
  try {
    // Check if tokens file exists
    if (!fs.existsSync(TOKENS_JSON_PATH)) {
      return res.status(404).json({
        error: 'Tokens file not found. Run extract-tokens first.',
        path: TOKENS_JSON_PATH
      });
    }

    const tokensData = JSON.parse(fs.readFileSync(TOKENS_JSON_PATH, 'utf8'));
    
    res.json({
      success: true,
      data: tokensData
    });

  } catch (error) {
    console.error('❌ Error reading tokens:', error);
    res.status(500).json({
      error: 'Failed to read tokens file',
      details: error.message
    });
  }
});

/**
 * GET /api/users/summary
 * Get users summary data
 */
router.get('/summary', (req, res) => {
  try {
    // Check if summary file exists
    if (!fs.existsSync(USERS_SUMMARY_PATH)) {
      return res.status(404).json({
        error: 'Summary file not found. Run extract-tokens first.',
        path: USERS_SUMMARY_PATH
      });
    }

    const summaryData = JSON.parse(fs.readFileSync(USERS_SUMMARY_PATH, 'utf8'));
    
    res.json({
      success: true,
      data: summaryData
    });

  } catch (error) {
    console.error('❌ Error reading summary:', error);
    res.status(500).json({
      error: 'Failed to read summary file',
      details: error.message
    });
  }
});

/**
 * GET /api/users/csv-status
 * Check status of CSV files and data
 */
router.get('/csv-status', (req, res) => {
  try {
    const status = {
      csv: {
        exists: fs.existsSync(CSV_PATH),
        path: CSV_PATH,
        size: fs.existsSync(CSV_PATH) ? fs.statSync(CSV_PATH).size : 0
      },
      tokens: {
        exists: fs.existsSync(TOKENS_JSON_PATH),
        path: TOKENS_JSON_PATH,
        size: fs.existsSync(TOKENS_JSON_PATH) ? fs.statSync(TOKENS_JSON_PATH).size : 0
      },
      summary: {
        exists: fs.existsSync(USERS_SUMMARY_PATH),
        path: USERS_SUMMARY_PATH,
        size: fs.existsSync(USERS_SUMMARY_PATH) ? fs.statSync(USERS_SUMMARY_PATH).size : 0
      }
    };

    // If CSV exists, get basic stats
    if (status.csv.exists) {
      try {
        const csvContent = fs.readFileSync(CSV_PATH, 'utf8');
        const parsed = Papa.parse(csvContent, { header: true });
        const users = parsed.data.filter(u => u.user_id);
        
        status.csv.userCount = users.length;
        status.csv.successfulLogins = users.filter(u => u.login_status === 'success').length;
        status.csv.failedLogins = users.filter(u => u.login_status === 'failed').length;
      } catch (parseError) {
        status.csv.parseError = parseError.message;
      }
    }

    res.json({
      success: true,
      status
    });

  } catch (error) {
    console.error('❌ Error checking status:', error);
    res.status(500).json({
      error: 'Failed to check file status',
      details: error.message
    });
  }
});

module.exports = router;
