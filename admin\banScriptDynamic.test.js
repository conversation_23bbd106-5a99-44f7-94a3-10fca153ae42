import http from 'k6/http';
import { check, group } from 'k6';
import {
  ADMIN_BASE,
  ADMIN_AUTH_TOKEN,
  BASE_URL,
  EXCHANGE,
  OVERSEE_USER_ID
} from '../config/config.js';

const adminHeaders = {
  'Content-Type': 'application/json',
  Authorization: ADMIN_AUTH_TOKEN,
};

const userHeaders = {
  'Content-Type': 'application/json',
  Authorization: `Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySUQiOiJJVS1BTk9PVUMiLCJVc2VyUm9sZSI6IklVIiwiT3ZlcnNlZVVzZXJJRCI6IkFHLU9LSURBNiIsIlZpZXdPbmx5IjpmYWxzZSwiR2VuZXJhdGVkRm9yIjoiQUctT0tJREE2IiwiZXhwIjoxNzUwMjQyNjgyLCJpc3MiOiJUZXN0TmFtZSJ9._ZO8r02mI06VJrw8BC6Z3f2nobXCxmUEGdW-bnJj1TQ`,
};

const noAuthHeaders = {
  'Content-Type': 'application/json'
};

const badHeaders = {
  'Content-Type': 'application/json',
  Authorization: 'Bearer invalid-token'
};

export const options = {
  vus: 1,
  iterations: 1,
};

export default function () {
  goodBanScript();
  badBanScript();
}

// Legacy function names for backward compatibility
export const testBanFlowLegacy = goodBanScript;
export const testBadCasesLegacy = badBanScript;

export function goodBanScript() {
  group('✅ Ban Script - Good Flow', () => {
    const instrument = fetchFirstAvailableScript();
    if (!instrument) {
      // Use a sample instrument ID for testing when no available scripts are found
      const sampleInstrumentId = 1120250829452849; // Sample ID from your curl example
      const segment = EXCHANGE;
      testBanFlow(sampleInstrumentId, segment);
      return;
    }

    const instrumentId = instrument.financial_instrument_id;
    const segment = EXCHANGE; // Use exchange from config as segment

    testBanFlow(instrumentId, segment);
  });
}

export function badBanScript() {
  group('❌ Ban Script - Bad Scenarios', () => {
    const instrument = fetchFirstAvailableScript();
    if (!instrument) {
      // Use a sample instrument ID for testing when no available scripts are found
      const sampleInstrumentId = 1120250829452849; // Sample ID from your curl example
      const segment = EXCHANGE;
      testBadCases(sampleInstrumentId, segment);
      return;
    }

    const instrumentId = instrument.financial_instrument_id;
    const segment = EXCHANGE; // Use exchange from config as segment

    testBadCases(instrumentId, segment);
  });
}

function fetchFirstAvailableScript() {
  let instrument = null;

  group('📋 Fetch Script List', () => {
    const segment = EXCHANGE; // Use exchange from config as segment
    const res = http.get(
      `${ADMIN_BASE}/get_all_scripts_by_user?segment=${segment}&search_term=&page_size=10&page_number=1&admin_user_id=${OVERSEE_USER_ID}`,
      { headers: adminHeaders }
    );

    const ok = check(res, {
      'banScript GET /get_all_scripts_by_user → 200': (r) => r.status === 200,
      'banScript GET /get_all_scripts_by_user → has data': (r) => {
        try {
          const json = r.json();
          return json.data && Array.isArray(json.data);
        } catch (e) {
          console.error('banScript GET /get_all_scripts_by_user → JSON parse error:', e.message);
          return false;
        }
      }
    });

    if (!ok) {
      console.error('banScript GET /get_all_scripts_by_user failed:', res.status, res.body);
      return;
    }

    try {
      const parsed = res.json();
      const decoded = parsed.data.map((s) => JSON.parse(s));
      instrument = decoded.find((s) => s.fetchPrice && !s.is_banned);

      // Script found or not found - no logging needed
    } catch (e) {
      console.error('banScript GET /get_all_scripts_by_user → Failed to parse script list:', e.message);
      return null;
    }
  });

  return instrument;
}

function testBanFlow(instrumentId, segment) {
  group('🚫 Ban Script + Disable Price Fetch + Verify Order Block + Restore', () => {
    // Step 1: Ban the script using query parameters
    const banUrl = `${ADMIN_BASE}/update_banned_script?financial_instrument_id=${instrumentId}&is_banned=true&segment=${segment}`;

    const banRes = http.put(banUrl, null, {
      headers: adminHeaders,
    });

    const banOk = check(banRes, {
      'banScript PUT /update_banned_script → 200 (ban)': (r) => r.status === 200,
      'banScript PUT /update_banned_script → success response (ban)': (r) => {
        try {
          const json = r.json();
          return json.message && json.message.includes("successfully");
        } catch (e) {
          console.error('banScript PUT /update_banned_script → JSON parse error (ban):', e.message);
          return false;
        }
      },
      'banScript PUT /update_banned_script → instrument banned (ban)': (r) => {
        try {
          const json = r.json();
          return json.instrument && json.instrument.is_banned === true;
        } catch (e) {
          console.error('banScript PUT /update_banned_script → instrument check error (ban):', e.message);
          return false;
        }
      }
    });

    if (!banOk) {
      console.error('banScript PUT /update_banned_script failed (ban):', banRes.status, banRes.body);
      return;
    }

    // Step 2: Disable price fetch
    const fetchPayload = JSON.stringify({
      is_fetch_enabled: false,
      segment: segment
    });

    const fetchRes = http.put(`${ADMIN_BASE}/update_fetch_price?financial_instrument_id=${instrumentId}`, fetchPayload, {
      headers: adminHeaders,
    });

    const fetchOk = check(fetchRes, {
      'banScript PUT /update_fetch_price → 200 (disable)': (r) => r.status === 200,
      'banScript PUT /update_fetch_price → success response (disable)': (r) => {
        try {
          const json = r.json();
          return json.message && json.message.includes("successfully");
        } catch (e) {
          console.error('banScript PUT /update_fetch_price → JSON parse error (disable):', e.message);
          return false;
        }
      }
    });

    if (!fetchOk) {
      console.error('banScript PUT /update_fetch_price failed (disable):', fetchRes.status, fetchRes.body);
    }

    // Step 3: Try placing market order (should be blocked)
    const orderPayload = JSON.stringify({
      instrumentId,
      exchange: segment,
      quantity: 1,
      OrderType: 'MARKET',
      buy: true,
    });

    const orderRes = http.post(
      `${BASE_URL}/api/user/makeOrder`,
      orderPayload,
      { headers: userHeaders }
    );

    const orderOk = check(orderRes, {
      'banScript POST /makeOrder → 400 (banned script)': (r) => r.status === 400,
      'banScript POST /makeOrder → banned script message': (r) => {
        try {
          const json = r.json();
          return json.message === 'Banned Script:Only Allowed Close' && json.status === 'fail';
        } catch (e) {
          console.error('banScript POST /makeOrder → JSON parse error:', e.message);
          return false;
        }
      }
    });

    if (!orderOk) {
      console.error('banScript POST /makeOrder failed (banned script test):', orderRes.status, orderRes.body);
    }

    // Step 4: Revert bans (cleanup)
    const unbanUrl = `${ADMIN_BASE}/update_banned_script?financial_instrument_id=${instrumentId}&is_banned=false&segment=${segment}`;
    const unbanRes = http.put(unbanUrl, null, { headers: adminHeaders });

    const unbanOk = check(unbanRes, {
      'banScript PUT /update_banned_script → 200 (unban)': (r) => r.status === 200,
      'banScript PUT /update_banned_script → instrument unbanned (unban)': (r) => {
        try {
          const json = r.json();
          return json.instrument && json.instrument.is_banned === false;
        } catch (e) {
          console.error('banScript PUT /update_banned_script → instrument check error (unban):', e.message);
          return false;
        }
      }
    });

    if (!unbanOk) {
      console.error('banScript PUT /update_banned_script failed (unban):', unbanRes.status, unbanRes.body);
    }

    const enablePayload = JSON.stringify({
      is_fetch_enabled: true,
      segment: segment
    });

    const enableRes = http.put(`${ADMIN_BASE}/update_fetch_price?financial_instrument_id=${instrumentId}`, enablePayload, { headers: adminHeaders });

    const enableOk = check(enableRes, {
      'banScript PUT /update_fetch_price → 200 (enable)': (r) => r.status === 200
    });

    if (!enableOk) {
      console.error('banScript PUT /update_fetch_price failed (enable):', enableRes.status, enableRes.body);
    }

    // Step 5: Verify market order works after unbanning
    const verifyOrderPayload = JSON.stringify({
      instrumentId,
      exchange: segment,
      quantity: 1,
      OrderType: 'MARKET',
      buy: true,
    });

    const verifyOrderRes = http.post(
      `${BASE_URL}/api/user/makeOrder`,
      verifyOrderPayload,
      { headers: userHeaders }
    );

    const verifyOrderOk = check(verifyOrderRes, {
      'banScript POST /makeOrder → 200 (after unban)': (r) => r.status === 200,
      'banScript POST /makeOrder → order allowed (after unban)': (r) => {
        try {
          const json = r.json();
          // Order should be allowed now - check that it's NOT the banned message
          return json.message !== 'Banned Script:Only Allowed Close';
        } catch (e) {
          console.error('banScript POST /makeOrder → JSON parse error (after unban):', e.message);
          return false;
        }
      }
    });

    if (!verifyOrderOk) {
      console.error('banScript POST /makeOrder failed (after unban verification):', verifyOrderRes.status, verifyOrderRes.body);
    }
  });
}

function testBadCases(instrumentId, segment) {
  group('🚫 Ban Script - Bad Scenarios', () => {

    // Test helper function for PUT requests with query parameters
    const test = (desc, url, testHeaders, expectedStatus = 400) => {
      const res = http.put(url, null, { headers: testHeaders });
      const ok = check(res, {
        [`${desc} → ${expectedStatus}+`]: (r) => r.status >= expectedStatus,
      });
      if (!ok) {
        console.error(`${desc} failed →`, res.status, res.body);
      }
    };

    const testPost = (desc, url, payload, testHeaders, expectedStatus = 400) => {
      const res = http.post(url, payload, { headers: testHeaders });
      const ok = check(res, {
        [`${desc} → ${expectedStatus}+`]: (r) => r.status >= expectedStatus,
      });
      if (!ok) {
        console.error(`${desc} failed →`, res.status, res.body);
      }
    };

    // Test missing financial_instrument_id parameter
    test(
      'banScript PUT /update_banned_script → missing financial_instrument_id',
      `${ADMIN_BASE}/update_banned_script?is_banned=true&segment=${segment}`,
      adminHeaders
    );

    // Test missing is_banned parameter
    test(
      'banScript PUT /update_banned_script → missing is_banned',
      `${ADMIN_BASE}/update_banned_script?financial_instrument_id=${instrumentId}&segment=${segment}`,
      adminHeaders
    );

    // Test missing segment parameter
    test(
      'banScript PUT /update_banned_script → missing segment',
      `${ADMIN_BASE}/update_banned_script?financial_instrument_id=${instrumentId}&is_banned=true`,
      adminHeaders
    );

    // Test invalid instrument ID
    test(
      'banScript PUT /update_banned_script → invalid instrument ID',
      `${ADMIN_BASE}/update_banned_script?financial_instrument_id=-1&is_banned=true&segment=${segment}`,
      adminHeaders
    );

    // Test invalid segment (API may be permissive)


   

    // Test invalid is_banned value
    test(
      'banScript PUT /update_banned_script → invalid is_banned',
      `${ADMIN_BASE}/update_banned_script?financial_instrument_id=${instrumentId}&is_banned=invalid&segment=${segment}`,
      adminHeaders
    );

    // Test missing authentication
    test(
      'banScript PUT /update_banned_script → no auth',
      `${ADMIN_BASE}/update_banned_script?financial_instrument_id=${instrumentId}&is_banned=true&segment=${segment}`,
      noAuthHeaders,
      403
    );

    // Test invalid authentication
    test(
      'banScript PUT /update_banned_script → bad token',
      `${ADMIN_BASE}/update_banned_script?financial_instrument_id=${instrumentId}&is_banned=true&segment=${segment}`,
      badHeaders,
      401
    );

    // Test price fetch with bad auth (using correct format with JSON body)
    const testFetchPrice = (desc, testHeaders, expectedStatus = 400) => {
      const payload = JSON.stringify({
        is_fetch_enabled: false,
        segment: segment
      });
      const res = http.put(`${ADMIN_BASE}/update_fetch_price?financial_instrument_id=${instrumentId}`, payload, { headers: testHeaders });
      const ok = check(res, {
        [`${desc} → ${expectedStatus}+`]: (r) => r.status >= expectedStatus,
      });
      if (!ok) {
        console.error(`${desc} failed →`, res.status, res.body);
      }
    };

    testFetchPrice(
      'banScript PUT /update_fetch_price → no auth',
      noAuthHeaders,
      403
    );

    testFetchPrice(
      'banScript PUT /update_fetch_price → bad token',
      badHeaders,
      401
    );

    // Test market order with bad token
    testPost(
      'banScript POST /makeOrder → bad token',
      `${BASE_URL}/api/user/makeOrder`,
      JSON.stringify({
        instrumentId,
        exchange: segment,
        quantity: 1,
        OrderType: 'MARKET',
        buy: true,
      }),
      badHeaders,
      401
    );

    // Test market order with no auth
    testPost(
      'banScript POST /makeOrder → no auth',
      `${BASE_URL}/api/user/makeOrder`,
      JSON.stringify({
        instrumentId,
        exchange: segment,
        quantity: 1,
        OrderType: 'MARKET',
        buy: true,
      }),
      noAuthHeaders,
      403
    );

    // Test market order with invalid payload
    testPost(
      'banScript POST /makeOrder → invalid payload',
      `${BASE_URL}/api/user/makeOrder`,
      JSON.stringify({
        instrumentId: -1,
        exchange: "INVALID",
        quantity: -1,
        OrderType: 'INVALID_TYPE',
        buy: "not_boolean",
      }),
      userHeaders
    );
  });
}
