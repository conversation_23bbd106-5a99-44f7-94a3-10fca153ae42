
         /\      Grafana   /‾‾/  
    /\  /  \     |\  __   /  /   
   /  \/    \    | |/ /  /   ‾‾\ 
  /          \   |   (  |  (‾)  |
 / __________ \  |_|\_\  \_____/ 

time="2025-06-16T22:46:24+05:30" level=info msg="✅ Loaded 5 users from CSV" source=console
     execution: local
        script: C:\Users\<USER>\Documents\k6\k6\scenarios\main.test.js
        output: -

     scenarios: (100.00%) 1 scenario, 1 max VUs, 10m30s max duration (incl. graceful stop):
              * default: 1 iterations shared among 1 VUs (maxDuration: 10m0s, gracefulStop: 30s)

time="2025-06-16T22:46:24+05:30" level=info msg="🚀 Running USER tests with mode: scenario" source=console
time="2025-06-16T22:46:24+05:30" level=info msg="📋 Scenarios selected: buyGoodLimitOrderFlow, buyGoodMarketScenario, sellGoodLimitOrderFlow, sellGoodMarketOrder, buyGoodStopLossOrderFlow, sellGoodStopLossOrderFlow, goodWatchlist, goodUserTransactions, goodChangePassword, testGoodLogins, testGoodRegister, buyBadLimitOrderFlow, buyBadMarketScenario, sellBadLimitOrderFlow, sellBadMarketOrder, buyBadStopLossOrderFlow, sellBadStopLossOrderFlow, badWatchlist, testBadRegister, badUserTransactions, badChangePassword, testBadLogins, testGetInstruments, testMarketOrders, testOtherCases" source=console
time="2025-06-16T22:46:25+05:30" level=error msg="Error in /api/auth/login (existing user): Response: {\n  \"message\": \"Invalid credentials\",\n  \"status\": \"fail\"\n}" source=console

running (00m01.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m01.0s/10m0s  0/1 shared iters

running (00m02.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m02.0s/10m0s  0/1 shared iters

running (00m03.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m03.0s/10m0s  0/1 shared iters

running (00m04.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m04.0s/10m0s  0/1 shared iters

running (00m05.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m05.0s/10m0s  0/1 shared iters

running (00m06.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m06.0s/10m0s  0/1 shared iters

running (00m07.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m07.0s/10m0s  0/1 shared iters
time="2025-06-16T22:46:32+05:30" level=info msg="✅ Using static auth for scenario testing (VU: 1)" source=console

running (00m08.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m08.0s/10m0s  0/1 shared iters

running (00m09.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m09.0s/10m0s  0/1 shared iters

running (00m10.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m10.0s/10m0s  0/1 shared iters
time="2025-06-16T22:46:35+05:30" level=error msg="buyLimitOrder_modifyOrder Payload: {\n  \"exchange\": \"MCX\",\n  \"orderId\": 1971,\n  \"triggerPrice\": 6057.36,\n  \"quantity\": 2\n}" source=console
time="2025-06-16T22:46:35+05:30" level=error msg="buyLimitOrder_modifyOrder Response: 400 {\"message\":\"Price should be within acceptable circuit limits for exchange MCX\",\"status\":\"fail\"}" source=console
time="2025-06-16T22:46:35+05:30" level=info msg="✅ Using static auth for scenario testing (VU: 1)" source=console

running (00m11.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m11.0s/10m0s  0/1 shared iters

running (00m12.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m12.0s/10m0s  0/1 shared iters
time="2025-06-16T22:46:37+05:30" level=info msg="✅ Using static auth for scenario testing (VU: 1)" source=console

running (00m13.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m13.0s/10m0s  0/1 shared iters
time="2025-06-16T22:46:37+05:30" level=info msg="🎯 Using instrumentId: 1120250618445825, LastPrice: 6183, UpperCkt: 0" source=console
time="2025-06-16T22:46:37+05:30" level=error msg="buyStopLossOrder Skipping: Invalid instrument price data → LastPrice: 6183, UpperCkt: 0" source=console
time="2025-06-16T22:46:37+05:30" level=info msg="✅ Using static auth for scenario testing (VU: 1)" source=console

running (00m14.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m14.0s/10m0s  0/1 shared iters
time="2025-06-16T22:46:38+05:30" level=error msg="📤 buyStopLossOrder_STOPLOSS → 400 on too low triggerPrice Payload: {\n  \"instrumentId\": 1120250618445825,\n  \"exchange\": \"MCX\",\n  \"quantity\": 1,\n  \"OrderType\": \"STOPLOSS\",\n  \"buy\": true,\n  \"triggerPrice\": 0.06\n}" source=console
time="2025-06-16T22:46:38+05:30" level=error msg="📥 buyStopLossOrder_STOPLOSS → 400 on too low triggerPrice Response: 200 {\"message\":\"STOPLOSS order placed. Funds blocked: 0.000600\",\"status\":\"success\",\"time\":\"2025-06-16T17:16:39.776008526Z\"}" source=console
time="2025-06-16T22:46:38+05:30" level=error msg="📤 buyStopLossOrder_STOPLOSS → 400 on triggerPrice above upper circuit Payload: {\n  \"instrumentId\": 1120250618445825,\n  \"exchange\": \"MCX\",\n  \"quantity\": 1,\n  \"OrderType\": \"STOPLOSS\",\n  \"buy\": true,\n  \"triggerPrice\": 12366\n}" source=console
time="2025-06-16T22:46:38+05:30" level=error msg="📥 buyStopLossOrder_STOPLOSS → 400 on triggerPrice above upper circuit Response: 200 {\"message\":\"STOPLOSS order placed. Funds blocked: 123.660000\",\"status\":\"success\",\"time\":\"2025-06-16T17:16:39.980832594Z\"}" source=console

running (00m15.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m15.0s/10m0s  0/1 shared iters
time="2025-06-16T22:46:39+05:30" level=info msg="✅ Using static auth for scenario testing (VU: 1)" source=console
time="2025-06-16T22:46:40+05:30" level=info msg="[sellLimitOrder] 💰 Net Balance BEFORE: 68811.7743" source=console
time="2025-06-16T22:46:40+05:30" level=error msg="sellLimitOrder Skipping: Invalid instrument price data → LastPrice: 6182, LowerCkt: 0" source=console
time="2025-06-16T22:46:40+05:30" level=info msg="✅ Using static auth for scenario testing (VU: 1)" source=console

running (00m16.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m16.0s/10m0s  0/1 shared iters

running (00m17.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m17.0s/10m0s  0/1 shared iters
time="2025-06-16T22:46:42+05:30" level=info msg="✅ Using static auth for scenario testing (VU: 1)" source=console
time="2025-06-16T22:46:42+05:30" level=error msg="sellStopLossOrder: Invalid instrument data → LastPrice: 6182, UpperCkt: 0" source=console
time="2025-06-16T22:46:42+05:30" level=info msg="✅ Using static auth for scenario testing (VU: 1)" source=console

running (00m18.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m18.0s/10m0s  0/1 shared iters

running (00m19.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m19.0s/10m0s  0/1 shared iters

running (00m20.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m20.0s/10m0s  0/1 shared iters
time="2025-06-16T22:46:45+05:30" level=info msg="✅ buyGoodMarketScenario: Using user: IU-ANOOUC (VU: 1)" source=console

running (00m21.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m21.0s/10m0s  0/1 shared iters

running (00m22.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m22.0s/10m0s  0/1 shared iters

running (00m23.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m23.0s/10m0s  0/1 shared iters

running (00m24.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m24.0s/10m0s  0/1 shared iters

running (00m25.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m25.0s/10m0s  0/1 shared iters

running (00m26.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m26.0s/10m0s  0/1 shared iters
time="2025-06-16T22:46:51+05:30" level=info msg="✅ Using static auth for scenario testing (VU: 1)" source=console

running (00m27.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m27.0s/10m0s  0/1 shared iters

running (00m28.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m28.0s/10m0s  0/1 shared iters

running (00m29.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m29.0s/10m0s  0/1 shared iters
time="2025-06-16T22:46:54+05:30" level=info msg="✅ Using static auth for scenario testing (VU: 1)" source=console

running (00m30.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m30.0s/10m0s  0/1 shared iters

running (00m31.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m31.0s/10m0s  0/1 shared iters
time="2025-06-16T22:46:56+05:30" level=info msg="✅ Using static auth for scenario testing (VU: 1)" source=console

running (00m32.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m32.0s/10m0s  0/1 shared iters

running (00m33.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m33.0s/10m0s  0/1 shared iters

running (00m34.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m34.0s/10m0s  0/1 shared iters

running (00m35.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m35.0s/10m0s  0/1 shared iters

running (00m36.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m36.0s/10m0s  0/1 shared iters
time="2025-06-16T22:47:01+05:30" level=info msg="✅ Using static auth for scenario testing (VU: 1)" source=console

running (00m37.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m37.0s/10m0s  0/1 shared iters

running (00m38.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m38.0s/10m0s  0/1 shared iters

running (00m39.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m39.0s/10m0s  0/1 shared iters

running (00m40.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m40.0s/10m0s  0/1 shared iters

running (00m41.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m41.0s/10m0s  0/1 shared iters

running (00m42.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m42.0s/10m0s  0/1 shared iters
time="2025-06-16T22:47:06+05:30" level=info msg="✅ Using static auth for scenario testing (VU: 1)" source=console
time="2025-06-16T22:47:07+05:30" level=info msg="✅ Using static auth for scenario testing (VU: 1)" source=console

running (00m43.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m43.0s/10m0s  0/1 shared iters
time="2025-06-16T22:47:07+05:30" level=info msg="✅ Using static auth for scenario testing (VU: 1)" source=console
time="2025-06-16T22:47:07+05:30" level=info msg="✅ Using static auth for scenario testing (VU: 1)" source=console

running (00m44.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m44.0s/10m0s  0/1 shared iters

running (00m45.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m45.0s/10m0s  0/1 shared iters


  █ TOTAL RESULTS 

    checks_total.......................: 152    3.340689/s
    checks_succeeded...................: 93.42% 142 out of 152
    checks_failed......................: 6.57%  10 out of 152

    ✗ login : valid user → 200
      ↳  0% — ✓ 0 / ✗ 1
    ✗ login : status = success
      ↳  50% — ✓ 1 / ✗ 1
    ✗ login : password_reset = false
      ↳  0% — ✓ 0 / ✗ 1
    ✓ login : valid user (reset) → 200
    ✓ login : password_reset = true
    ✗ login : wrong password → 400 with fail
      ↳  0% — ✓ 0 / ✗ 1
    ✗ login : invalid user → 400 with fail
      ↳  0% — ✓ 0 / ✗ 1
    ✓ login : missing password → 400+
    ✓ login : missing identifier → 400+
    ✓ login : malformed JSON → 400+
    ✓ login : no Content-Type → 200
    ✓ Register : with overseeUserID full payload → 201
    ✓ Register : with overseeUserID status = success
    ✓ Register : with only email and password → 201
    ✓ Register : with only email and password status = success
    ✓ Register : duplicate email → 400+ or fail status
    ✓ Register : missing password → 400+ or fail status
    ✓ Register : missing email → 400+ or fail status
    ✓ Register : invalid email format → 400+ or fail status
    ✓ Register : empty request body → 400
    ✗ no Content-Type → 400+
      ↳  0% — ✓ 0 / ✗ 1
    ✓ buyLimitOrder GET wallet before → 200
    ✓ buyLimitOrder GET wallet before → has wallets
    ✓ buyLimitOrder POST getInstruments → 200
    ✓ buyLimitOrder POST getInstruments → has instruments
    ✓ buyLimitOrder POST makeOrder → 200
    ✓ buyLimitOrder POST makeOrder → success
    ✓ buyLimitOrder POST makeOrder → funds blocked
    ✓ wallet after → 200
    ✓ buyLimitOrder_getPortfolio → 200
    ✓ buyLimitOrder_netBalance reduced after LIMIT order
    ✓ buyLimitOrder_openOrders → 200
    ✗ buyLimitOrder_modifyOrder → 200
      ↳  0% — ✓ 0 / ✗ 1
    ✗ buyLimitOrder_modifyOrder → success
      ↳  0% — ✓ 0 / ✗ 1
    ✓ buyLimitOrder POST makeOrder → missing triggerPrice → 400+
    ✓ buyLimitOrder POST makeOrder → invalid instrumentId → 400+
    ✓ buyLimitOrder POST makeOrder → invalid quantity → 400+
    ✓ buyLimitOrder POST makeOrder → malformed JSON → 400+
    ✓ buyLimitOrder POST makeOrder → no auth → 401+
    ✓ buyLimitOrder POST makeOrder → bad token → 401+
    ✓ buyLimitOrder POST getInstruments → no auth → 401+
    ✓ buyLimitOrder POST getInstruments → bad token → 401+
    ✓ wallet before → 200
    ✓ buyStopLossOrder_getInstruments → 200
    ✓ buyStopLossOrder_STOPLOSS → 400 on missing triggerPrice
    ✓ buyStopLossOrder_STOPLOSS → fail on invalid instrumentId
    ✗ buyStopLossOrder_STOPLOSS → 400 on too low triggerPrice
      ↳  0% — ✓ 0 / ✗ 1
    ✗ buyStopLossOrder_STOPLOSS → 400 on triggerPrice above upper circuit
      ↳  0% — ✓ 0 / ✗ 1
    ✓ sellLimitOrder_wallet before → 200
    ✓ sellLimitOrder_getInstruments → 200
    ✓ sellLimitOrder_makeOrder → 400/422 on missing triggerPrice
    ✓ sellLimitOrder_makeOrder → fail on invalid instrumentId
    ✓ sellLimitOrder_makeOrder → 400 on too low triggerPrice
    ✓ sellStopLossOrder GET wallet before → 200
    ✓ sellStopLossOrder GET wallet before → has wallets
    ✓ sellStopLossOrder POST getInstruments → 200
    ✓ sellStopLossOrder POST getInstruments → has instruments
    ✓ sellStopLossOrder POST makeOrder → missing triggerPrice → 400+
    ✓ sellStopLossOrder POST makeOrder → invalid instrumentId → 400+
    ✓ sellStopLossOrder POST makeOrder → invalid quantity → 400+
    ✓ sellStopLossOrder POST makeOrder → malformed JSON → 400+
    ✓ sellStopLossOrder POST makeOrder → no auth → 401+
    ✓ sellStopLossOrder POST makeOrder → bad token → 401+
    ✓ sellStopLossOrder POST getInstruments → no auth → 401+
    ✓ sellStopLossOrder POST getInstruments → bad token → 401+
    ✓ buyGoodMarketScenario GET wallet before → 200
    ✓ buyGoodMarketScenario GET wallet before → has wallets
    ✓ buyGoodMarketScenario POST getInstruments → 200
    ✓ buyGoodMarketScenario POST getInstruments → has instruments
    ✓ buyGoodMarketScenario POST makeOrder → 200
    ✓ buyGoodMarketScenario POST makeOrder → success
    ✓ buyGoodMarketScenario GET wallet after → 200
    ✓ buyGoodMarketScenario GET wallet after → has wallets
    ✓ buyGoodMarketScenario → net_balance reduced after order
    ✓ buyGoodMarketScenario POST getPortfolio → 200
    ✓ buyGoodMarketScenario POST getPortfolio → has portfolios
    ✓ buyGoodMarketScenario → portfolio includes instrument
    ✓ buyBadMarketScenario POST getInstruments → invalid token → 401+
    ✓ buyBadMarketScenario POST getInstruments → no auth → 401+
    ✓ buyBadMarketScenario POST makeOrder → invalid token → 401+
    ✓ buyBadMarketScenario POST makeOrder → no auth → 401+
    ✓ buyBadMarketScenario POST makeOrder → missing instrumentId → 400+
    ✓ buyBadMarketScenario POST makeOrder → invalid instrumentId → 400+
    ✓ buyBadMarketScenario POST makeOrder → invalid quantity → 400+
    ✓ buyBadMarketScenario POST makeOrder → malformed JSON → 400+
    ✓ buyBadMarketScenario POST getInstruments → malformed JSON → 400+
    ✓ buyBadMarketScenario POST getPortfolio → invalid token → 401+
    ✓ buyBadMarketScenario POST getPortfolio → no auth → 401+
    ✓ buyBadMarketScenario POST getPortfolio → malformed JSON → 400+
    ✓ sellMarketOrder_wallet before → 200
    ✓ sellMarketOrder_getInstruments → 200
    ✓ sellMarketOrder_makeOrder → 200
    ✓ sellMarketOrder_makeOrder → success
    ✓ sellMarketOrder_wallet after → 200
    ✓ sellMarketOrder_balance changed after short cover
    ✓ sellMarketOrder_getPortfolio → 200
    ✓ sellMarketOrder_getPortfolio → includes instrument
    ✓ getInstruments → 401 on invalid token
    ✓ sellMarketOrder_makeOrder → 400/422 on missing instrumentId
    ✓ sellMarketOrder_makeOrder → fails with invalid instrumentId
    ✓ sellMarketOrder_getInstruments → 403 on missing token
    ✓ addToWatchlist_getInstruments → 200
    ✓ addToWatchlist_getInstruments → has instruments
    ✓ addToWatchlist → 200
    ✓ addToWatchlist → success
    ✓ addToWatchlist_watchlist → 200
    ✓ addToWatchlist_watchlist checking addded or not→ includes instrument
    ✓ removeFromWatchlist → 200
    ✓ removeFromWatchlist → success
    ✓ removeFromWatchlist_watchlist (after remove) → 200
    ✓ removeFromWatchlist_watchlist → no longer includes instrument
    ✓ addToWatchlist_missing instrumentId → 400+
    ✓ addToWatchlist_missing exchange → 400+
    ✓ addToWatchlist_invalid instrumentId → 400+ or 200 (API permissive)
    ✓ addToWatchlist_invalid token → 401+
    ✓ addToWatchlist_no token → 403+
    ✓ addToWatchlist_malformed JSON (add) → 400+
    ✓ watchlist → missing exchange → 400+
    ✓ watchlist → bad token → 401+
    ✓ watchlist → no token → 403+
    ✓ watchlist → malformed body → 400+
    ✓ removeFromWatchlist → missing instrumentId → 400+
    ✓ removeFromWatchlist → missing exchange → 400+
    ✓ removeFromWatchlist → bad token → 401+
    ✓ removeFromWatchlist → no token → 403+
    ✓ removeFromWatchlist → malformed body → 400+
    ✓ invalid token → 401
    ✓ no token → 403
    ✓ invalid exchange → 400
    ✓ invalid request body → 400
    ✓ invalid order type → 400
    ✓ invalid instrumentId → 400
    ✓ quantity > per order limit → 400
    ✓ quantity > total allowed limit → 400
    ✓ insufficient balance → 400
    ✓ userTransactions POST /api/user/transactions → 200
    ✓ userTransactions POST /api/user/transactions → response is array
    ✓ userTransactions POST /api/user/transactions → transactions valid
    ✓ missing currency → 400 or valid empty
    ✓ updatePassword Good Flow → Skipped (unknown current password)
    ✓ updatePassword POST /api/user/changePassword → wrong current password → 400+
    ✓ updatePassword POST /api/user/changePassword → same password → 400+
    ✓ updatePassword POST /api/user/changePassword → missing newPassword → 400+
    ✓ updatePassword POST /api/user/changePassword → missing currentPassword → 400+
    ✓ updatePassword POST /api/user/changePassword → malformed JSON → 400+
    ✓ updatePassword POST /api/user/changePassword → no auth → 401+
    ✓ updatePassword POST /api/user/changePassword → bad token → 401+

    HTTP
    http_req_duration.......................................................: avg=207.69ms min=185.76ms med=191.32ms max=595.63ms p(90)=213.34ms p(95)=286.31ms
      { expected_response:true }............................................: avg=223.06ms min=190.55ms med=197.21ms max=582.4ms  p(90)=281.05ms p(95)=346.64ms
    http_req_failed.........................................................: 68.85% 84 out of 122
    http_reqs...............................................................: 122    2.681343/s

    EXECUTION
    iteration_duration......................................................: avg=45.49s   min=45.49s   med=45.49s   max=45.49s   p(90)=45.49s   p(95)=45.49s  
    iterations..............................................................: 1      0.021978/s
    vus.....................................................................: 1      min=1         max=1
    vus_max.................................................................: 1      min=1         max=1

    NETWORK
    data_received...........................................................: 87 kB  1.9 kB/s
    data_sent...............................................................: 17 kB  377 B/s




running (00m45.5s), 0/1 VUs, 1 complete and 0 interrupted iterations
default ✓ [ 100% ] 1 VUs  00m45.5s/10m0s  1/1 shared iters
