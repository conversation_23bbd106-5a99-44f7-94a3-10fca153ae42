param (
    [string]$logPath = "logs/createUser-7.log",
    [string]$csvPath = "data/users.csv"
)

$output = @()

Get-Content $logPath | ForEach-Object {
    if ($_ -match 'msg="(.+)"') {
        $escapedJson = $matches[1]
        $json = $escapedJson -replace '\\"', '"'
        try {
            $obj = $json | ConvertFrom-Json
            $output += [PSCustomObject]@{
                user_id  = $obj.user_id
                password = $obj.password
                email    = $obj.email
            }
        } catch {
            Write-Warning "Failed to parse: $json"
        }
    }
}

if ($output.Count -gt 0) {
    $output | Export-Csv -Path $csvPath -NoTypeInformation
    Write-Host "`n✅ Saved CSV to: $csvPath`n"
} else {
    Write-Warning "No valid JSON entries found in log."
}
