import http from 'k6/http';
import { check, group, sleep } from 'k6';
import * as cfg from '../config/config.js'; // Expects ADMIN_BASE, ADMIN_AUTH_TOKEN, EXCHANGE, OVERSEE_USER_ID
import { SharedArray } from 'k6/data';
import Papa from 'https://jslib.k6.io/papaparse/5.1.1/index.js';

// Dynamically load users from CSV
const users = new SharedArray('users', () => {
  const csv = open('../data/users.csv');
  return Papa.parse(csv, { header: true }).data.filter(u => u.user_id);
});

const headers = {
  accept: 'application/json',
  'Content-Type': 'application/json',
  Authorization: cfg.ADMIN_AUTH_TOKEN,
};

export const options = {
  vus: users.length,         // Automatically overridden by API layer
  iterations: users.length,  // Automatically overridden by API layer
};

export default function userMappingGoodFlow() {
  const user = users[__VU - 1];

  group(`✅ Enabling user: ${user.user_id}`, () => {
    // 1. Get instruments
    const instRes = http.get(`${cfg.ADMIN_BASE}/userzinstruments/${cfg.EXCHANGE}?userId=${user.user_id}&page=1&page_size=5`, { headers });
    const instrumentsOk = check(instRes, { 'Get Instruments → 200': r => r.status === 200 });
    if (!instrumentsOk) return console.error('❌ Failed to fetch instruments');

    const instruments = instRes.json()?.rows?.slice(0, 2) ?? [];
    if (!instruments.length) return console.error('❌ No instruments found');

    // 2. Token mapping
    const stocks = instruments.map(i => ({
      id: i.financial_instrument_id,
      name: i.trading_symbol,
      instrument_type: i.instrument_type,
      exchange: i.exchange,
      margin: 1,
    }));

    const tokenRes = http.post(`${cfg.ADMIN_BASE}/user-token-mapping`, JSON.stringify({
      user_id: user.user_id,
      exchange: cfg.EXCHANGE,
      segment: cfg.EXCHANGE,
      stocks,
    }), { headers });

    const tokenOk = check(tokenRes, {
      'Token Mapping → 200': r => r.status === 200,
      'Token Mapping → Message OK': r => r.json()?.message?.includes('Updated'),
    });
    if (!tokenOk) return console.error('❌ Token mapping failed');

    // 3. Global margin update
    const marginRes = http.post(`${cfg.ADMIN_BASE}/global-margin-update`, JSON.stringify({
      user_id: user.user_id,
      exchange: cfg.EXCHANGE,
      segment: cfg.EXCHANGE,
      global_margin: '0.01',
    }), { headers });

    const marginOk = check(marginRes, {
      'Margin Update → 200': r => r.status === 200,
      'Margin → Message OK': r => r.json()?.message?.includes('updated'),
    });
    if (!marginOk) return console.error('❌ Margin update failed');

    // 4. Check agent wallet balance
    const agentWalletRes = http.get(`${cfg.ADMIN_BASE}/wallets/balance/${cfg.OVERSEE_USER_ID}`, { headers });
    const agentOk = check(agentWalletRes, { 'Agent Wallet → 200': r => r.status === 200 });
    if (!agentOk) return console.error('❌ Agent wallet check failed');

    const available = agentWalletRes.json()?.wallets?.find(w => w.exchange === cfg.EXCHANGE)?.balance || 0;
    const applyAmount = 10000;

    if (available < applyAmount) {
      return console.error(`❌ Insufficient agent balance: available = ${available}`);
    }

    // 5. Apply balance to user
    const applyRes = http.post(`${cfg.ADMIN_BASE}/user/applyBalance`, JSON.stringify({
      user_id: user.user_id,
      exchange: cfg.EXCHANGE,
      balance: `${applyAmount}`,
      oversee_id: cfg.OVERSEE_USER_ID,
      updated_by: cfg.OVERSEE_USER_ID,
    }), { headers });

    const applyOk = check(applyRes, {
      'Apply Balance → 200': r => r.status === 200,
      'Apply → Message OK': r => r.json()?.message?.includes('balances updated'),
    });
    console.log('Apply balance response:', applyRes.json());

    if (!applyOk) return console.error('❌ Apply balance failed');

    // 6. Verify user wallet
    const userWalletRes = http.get(`${cfg.ADMIN_BASE}/wallets/balance/${user.user_id}`, { headers });
    const walletOk = check(userWalletRes, {
      'User Wallet Check → 200': r => r.status === 200,
      'Wallet → Has Balance': r => {
        const bal = userWalletRes.json()?.wallets?.find(w => w.exchange === cfg.EXCHANGE)?.balance || 0;
        return bal >= applyAmount;
      }
    });

    if (!walletOk) return console.error('❌ Wallet not updated for user');

    console.log(`✅ Enabled user: ${user.user_id}`);
    sleep(1);
  });
}
