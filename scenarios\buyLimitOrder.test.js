import { sleep, check, group } from 'k6';
import http from 'k6/http';
import { BASE_URL, ADMIN_BASE, EXCHANGE } from '../config/config.js';
import { getUserSafe, safeJson } from '../utils/csvUserManager.js';



export const options = {
  vus: 1,
  iterations: 1,
};

export function buyGoodLimitOrderFlow() {
  group('✅ Buy Limit Order - Good Flow', () => {
    const { user_id, password, token } = getUserSafe();

    if (!user_id || !token) {
      console.error(`❌ buyGoodLimitOrderFlow: No valid user available for VU ${__VU}`);
      return;
    }

    const headers = {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    };

    // 1. Wallet BEFORE
    const walletBeforeRes = http.get(`${ADMIN_BASE}/wallets/balance/${user_id}`, { headers });
    const walletBeforeOk = check(walletBeforeRes, {
      'buyLimitOrder GET wallet before → 200': (r) => r.status === 200,
      'buyLimitOrder GET wallet before → has wallets': (r) => {
        try {
          const json = r.json();
          return Array.isArray(json.wallets) && json.wallets.length > 0;
        } catch (e) {
          console.error('buyLimitOrder GET wallet before → JSON parse error:', e.message);
          return false;
        }
      },
    });

    if (!walletBeforeOk) {
      console.error('buyLimitOrder GET wallet before failed:', walletBeforeRes.status, walletBeforeRes.body);
      return;
    }

    const walletBeforeJson = safeJson(walletBeforeRes);
    const netBefore = parseFloat(walletBeforeJson?.wallets?.find(w => w.exchange === EXCHANGE)?.net_balance || 0);

    if (netBefore <= 0) {
      console.error(`buyLimitOrder: Insufficient balance (${netBefore}) for user ${user_id}`);
      return;
    }

    // 2. Get Instrument
    const instPayload = JSON.stringify({ exchange: EXCHANGE, searchPattern: '', page: 1, pageSize: 1 });
    const instrumentRes = http.post(`${BASE_URL}/api/user/getInstruments`, instPayload, { headers });
    const instrumentOk = check(instrumentRes, {
      'buyLimitOrder POST getInstruments → 200': (r) => r.status === 200,
      'buyLimitOrder POST getInstruments → has instruments': (r) => {
        try {
          const json = r.json();
          return Array.isArray(json.instruments) && json.instruments.length > 0;
        } catch (e) {
          console.error('buyLimitOrder POST getInstruments → JSON parse error:', e.message);
          return false;
        }
      },
    });

    if (!instrumentOk) {
      console.error('buyLimitOrder POST getInstruments failed:', instrumentRes.status, instrumentRes.body);
      return;
    }

    const instrumentJson = safeJson(instrumentRes);
    const instrument = instrumentJson?.instruments?.[0];
    if (!instrument) {
      console.error('buyLimitOrder POST getInstruments: No instrument found');
      return;
    }

    const instrumentId = instrument.FinancialInstrumentID;
    const tradingSymbol = instrument.TradingSymbol;
    const lastPrice = parseFloat(instrument.LastPrice);
    const lowerCkt = parseFloat(instrument.LowerCkt);

    if (!lastPrice || lastPrice === 0) {
      console.error(`buyLimitOrder: Invalid instrument price → LastPrice: ${lastPrice}`);
      return;
    }

    // Use a conservative trigger price that's slightly below last price
    // If circuit breakers are 0 or invalid, use a safe percentage-based approach
    const triggerPrice = parseFloat((lastPrice * 0.98).toFixed(2));

    // Only check circuit breaker if it's valid (> 0)
    if (lowerCkt > 0 && triggerPrice <= lowerCkt) {
      console.error(`buyLimitOrder: triggerPrice (${triggerPrice}) ≤ lower circuit (${lowerCkt})`);
      return;
    }

    // 3. Place LIMIT order
    const orderPayload = {
      instrumentId,
      exchange: EXCHANGE,
      quantity: 1,
      OrderType: 'LIMIT',
      buy: true,
      triggerPrice,
    };
    const orderRes = http.post(`${BASE_URL}/api/user/makeOrder`, JSON.stringify(orderPayload), { headers });
    const orderOk = check(orderRes, {
      'buyLimitOrder POST makeOrder → 200': (r) => r.status === 200,
      'buyLimitOrder POST makeOrder → success': (r) => {
        try {
          const json = r.json();
          return json?.status === 'success';
        } catch (e) {
          console.error('buyLimitOrder POST makeOrder → JSON parse error:', e.message);
          return false;
        }
      },
      'buyLimitOrder POST makeOrder → funds blocked': (r) => {
        try {
          const json = r.json();
          const fundsMatch = json?.message?.match(/Funds blocked: ([\d.]+)/);
          return fundsMatch && parseFloat(fundsMatch[1]) > 0;
        } catch (e) {
          console.error('buyLimitOrder POST makeOrder → funds check error:', e.message);
          return false;
        }
      },
    });

    if (!orderOk) {
      console.error('buyLimitOrder POST makeOrder failed:', orderRes.status, orderRes.body);
      console.error('buyLimitOrder POST makeOrder payload:', JSON.stringify(orderPayload, null, 2));
      return;
    }

    sleep(1);

    // 4. Wallet AFTER
    const walletAfterRes = http.get(`${ADMIN_BASE}/wallets/balance/${user_id}`, { headers });
    const walletAfterOk = check(walletAfterRes, { 'wallet after → 200': (r) => r.status === 200 });
    if (!walletAfterOk) console.error('📥 Wallet After:', walletAfterRes.status, walletAfterRes.body);

    const walletAfterJson = safeJson(walletAfterRes);
    const netAfter = parseFloat(walletAfterJson?.wallets?.find(w => w.exchange === EXCHANGE)?.net_balance || 0);
   // console.log(`Net Balance AFTER: ${netAfter}`);

    // 5. Portfolio check for short-cover scenario
    const portfolioRes = http.post(`${BASE_URL}/api/user/getPortfolio`, JSON.stringify({ exchange: EXCHANGE }), { headers });
    const portfolioOk = check(portfolioRes, { 'buyLimitOrder_getPortfolio → 200': (r) => r.status === 200 });
    if (!portfolioOk) console.error('buyLimitOrder_getPortfolio:', portfolioRes.status, portfolioRes.body);

    const portfolioJson = safeJson(portfolioRes);
    const position = portfolioJson?.portfolios?.find(p => p.FinancialInstrumentID === instrumentId);
    const isShort = position && position.Quantity < 0;
    //console.log(`Position check: Qty = ${position?.Quantity ?? 'N/A'}, isShort = ${isShort}`);

    const label = `buyLimitOrder_netBalance ${isShort ? 'unchanged (short cover)' : 'reduced'} after LIMIT order`;

    const netBalanceCheck = check({}, {
      [label]: () => isShort ? netAfter === netBefore : netAfter < netBefore,
    });

    if (!netBalanceCheck) {
      console.error(
        `CHECK FAILED: ${label}\n` +
        `Expected: ${isShort ? 'netAfter === netBefore' : 'netAfter < netBefore'}\n` +
        `Actual: netBefore = ${netBefore}, netAfter = ${netAfter}`
      );
    }


    // 6. openOrders check
    const openRes = http.post(`${BASE_URL}/api/user/openOrders`, JSON.stringify({ exchange: EXCHANGE }), { headers });
    const openOk = check(openRes, { 'buyLimitOrder_openOrders → 200': (r) => r.status === 200 });
    if (!openOk) console.error('buyLimitOrder_openOrders:', openRes.status, openRes.body);

    const openJson = safeJson(openRes);
    const order = openJson?.orders?.find(o => o.instrumentId === instrumentId && o.orderType === 'LIMIT');
    if (!order) {
      console.error('buyLimitOrder_Order not found in openOrders');
      return;
    }
    const orderId = order.ID;
   // console.log(`📌 Order ID: ${orderId}`);

    // 7. Modify order
    const modifyPayload = {
      exchange: EXCHANGE,
      orderId,
      triggerPrice: triggerPrice - 1,
      quantity: 2,
    };
    const modifyRes = http.post(`${BASE_URL}/api/user/modifyOrder`, JSON.stringify(modifyPayload), { headers });
    const modifyOk = check(modifyRes, {
      'buyLimitOrder_modifyOrder → 200': (r) => r.status === 200,
      'buyLimitOrder_modifyOrder → success': (r) => r.json()?.status === 'success',
    });
    if (!modifyOk) {
      console.error('buyLimitOrder_modifyOrder Payload:', JSON.stringify(modifyPayload, null, 2));
      console.error('buyLimitOrder_modifyOrder Response:', modifyRes.status, modifyRes.body);
      return;
    }
    //console.log('Order modified');

    // 8. Cancel order
    const cancelPayload = { exchange: EXCHANGE, orderId };
    const cancelRes = http.post(`${BASE_URL}/api/user/cancelOrder`, JSON.stringify(cancelPayload), { headers });
    const cancelOk = check(cancelRes, {
      'buyLimitOrder_cancelOrder → 200': (r) => r.status === 200,
      'buyLimitOrder_cancelOrder → success': (r) => r.json()?.status === 'success',
    });
    if (!cancelOk) {
      console.error('buyLimitOrder_cancelOrder Payload:', JSON.stringify(cancelPayload, null, 2));
      console.error('buyLimitOrder_cancelOrder Response:', cancelRes.status, cancelRes.body);
      return;
    }
    //console.log('🗑️ Order cancelled');

    sleep(1);
  });
}


export function buyBadLimitOrderFlow() {
  group('❌ Buy Limit Order - Bad Scenarios', () => {
    const { user_id, password, token } = getUserSafe();

    if (!user_id || !token) {
      console.error(`❌ buyBadLimitOrderFlow: No valid user available for VU ${__VU}`);
      return;
    }

    const headers = {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    };

    const badHeaders = {
      'Content-Type': 'application/json',
      Authorization: 'Bearer invalid-token',
    };

    const noAuthHeaders = {
      'Content-Type': 'application/json',
    };

    // Test helper function
    const test = (desc, url, payload, testHeaders, expectedStatus = 400) => {
      const res = http.post(url, payload, { headers: testHeaders });
      const ok = check(res, {
        [`${desc} → ${expectedStatus}+`]: (r) => r.status >= expectedStatus,
      });
      if (!ok) {
        console.error(`${desc} failed →`, res.status, res.body);
      }
    };

    const invalidId = 999999999999;

    // Test missing triggerPrice
    test(
      'buyLimitOrder POST makeOrder → missing triggerPrice',
      `${BASE_URL}/api/user/makeOrder`,
      JSON.stringify({
        instrumentId: invalidId,
        exchange: EXCHANGE,
        quantity: 1,
        OrderType: 'LIMIT',
        buy: true
      }),
      headers
    );

    // Test invalid instrument ID
    test(
      'buyLimitOrder POST makeOrder → invalid instrumentId',
      `${BASE_URL}/api/user/makeOrder`,
      JSON.stringify({
        instrumentId: invalidId,
        exchange: EXCHANGE,
        quantity: 1,
        OrderType: 'LIMIT',
        buy: true,
        triggerPrice: 100,
      }),
      headers
    );

    // Test invalid quantity
    test(
      'buyLimitOrder POST makeOrder → invalid quantity',
      `${BASE_URL}/api/user/makeOrder`,
      JSON.stringify({
        instrumentId: 1,
        exchange: EXCHANGE,
        quantity: -1,
        OrderType: 'LIMIT',
        buy: true,
        triggerPrice: 100,
      }),
      headers
    );

    // Test malformed JSON
    test(
      'buyLimitOrder POST makeOrder → malformed JSON',
      `${BASE_URL}/api/user/makeOrder`,
      '{"instrumentId": 1, "exchange": "' + EXCHANGE + '", "quantity": invalid json',
      headers
    );

    // Test missing authentication
    test(
      'buyLimitOrder POST makeOrder → no auth',
      `${BASE_URL}/api/user/makeOrder`,
      JSON.stringify({
        instrumentId: 1,
        exchange: EXCHANGE,
        quantity: 1,
        OrderType: 'LIMIT',
        buy: true,
        triggerPrice: 100,
      }),
      noAuthHeaders,
      401
    );

    // Test invalid authentication
    test(
      'buyLimitOrder POST makeOrder → bad token',
      `${BASE_URL}/api/user/makeOrder`,
      JSON.stringify({
        instrumentId: 1,
        exchange: EXCHANGE,
        quantity: 1,
        OrderType: 'LIMIT',
        buy: true,
        triggerPrice: 100,
      }),
      badHeaders,
      401
    );

    // Test getInstruments with bad auth
    test(
      'buyLimitOrder POST getInstruments → no auth',
      `${BASE_URL}/api/user/getInstruments`,
      JSON.stringify({ exchange: EXCHANGE, searchPattern: '', page: 1, pageSize: 1 }),
      noAuthHeaders,
      401
    );

    test(
      'buyLimitOrder POST getInstruments → bad token',
      `${BASE_URL}/api/user/getInstruments`,
      JSON.stringify({ exchange: EXCHANGE, searchPattern: '', page: 1, pageSize: 1 }),
      badHeaders,
      401
    );

    sleep(1);
  });
}

export default function () {
  buyGoodLimitOrderFlow();
  buyBadLimitOrderFlow();
}
