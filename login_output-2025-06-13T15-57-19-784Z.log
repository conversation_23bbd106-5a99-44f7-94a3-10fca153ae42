
         /\      Grafana   /‾‾/  
    /\  /  \     |\  __   /  /   
   /  \/    \    | |/ /  /   ‾‾\ 
  /          \   |   (  |  (‾)  |
 / __________ \  |_|\_\  \_____/ 

     execution: local
        script: C:\Users\<USER>\Documents\k6\k6\scripts\login-from-csv.js
        output: -

     scenarios: (100.00%) 1 scenario, 1 max VUs, 10m30s max duration (incl. graceful stop):
              * default: 1 iterations shared among 1 VUs (maxDuration: 10m0s, gracefulStop: 30s)

time="2025-06-13T21:27:21+05:30" level=info msg="Token fetched for IU-WXVZAZ" source=console

running (00m01.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m01.0s/10m0s  0/1 shared iters
time="2025-06-13T21:27:22+05:30" level=info msg="Token fetched for IU-P9N3QV" source=console
time="2025-06-13T21:27:22+05:30" level=info msg="Token fetched for IU-L5SCT8" source=console

running (00m02.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m02.0s/10m0s  0/1 shared iters
time="2025-06-13T21:27:23+05:30" level=info msg="Token fetched for IU-JP3FM8" source=console
time="2025-06-13T21:27:23+05:30" level=info msg="Token fetched for IU-BQ569Y" source=console

running (00m03.0s), 1/1 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 1 VUs  00m03.0s/10m0s  0/1 shared iters
time="2025-06-13T21:27:23+05:30" level=info msg="Token fetched for IU-OZPIJ0" source=console
time="2025-06-13T21:27:24+05:30" level=info msg="Token fetched for IU-U3SHG8" source=console
time="2025-06-13T21:27:24+05:30" level=info source=console
time="2025-06-13T21:27:24+05:30" level=info msg="COPY BELOW CONTENT TO: /data/users_with_tokens.csv" source=console
time="2025-06-13T21:27:24+05:30" level=info msg="user_id,password,token" source=console
time="2025-06-13T21:27:24+05:30" level=info msg="IU-WXVZAZ,6149,eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySUQiOiJJVS1XWFZaQVoiLCJVc2VyUm9sZSI6IklVIiwiT3ZlcnNlZVVzZXJJRCI6IkFHLU9LSURBNiIsIlZpZXdPbmx5IjpmYWxzZSwiR2VuZXJhdGVkRm9yIjoiQUctT0tJREE2IiwiZXhwIjoxNzUwMDAzMDQxLCJpc3MiOiJUZXN0TmFtZSJ9.fwAU1D2OvuEn4AKxHmlnggk2v6mbrs4NHkMl5nG9D88" source=console
time="2025-06-13T21:27:24+05:30" level=info msg="IU-P9N3QV,4780,eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySUQiOiJJVS1QOU4zUVYiLCJVc2VyUm9sZSI6IklVIiwiT3ZlcnNlZVVzZXJJRCI6IkFHLU9LSURBNiIsIlZpZXdPbmx5IjpmYWxzZSwiR2VuZXJhdGVkRm9yIjoiQUctT0tJREE2IiwiZXhwIjoxNzUwMDAzMDQyLCJpc3MiOiJUZXN0TmFtZSJ9.Kd1XIJRg9KvW1SMZz47CIOu3Ab1SFFZwU8tpanQkn_k" source=console
time="2025-06-13T21:27:24+05:30" level=info msg="IU-L5SCT8,8848,eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySUQiOiJJVS1MNVNDVDgiLCJVc2VyUm9sZSI6IklVIiwiT3ZlcnNlZVVzZXJJRCI6IkFHLU9LSURBNiIsIlZpZXdPbmx5IjpmYWxzZSwiR2VuZXJhdGVkRm9yIjoiQUctT0tJREE2IiwiZXhwIjoxNzUwMDAzMDQyLCJpc3MiOiJUZXN0TmFtZSJ9.MWsvX4rx59uJL0ISH-e2QJvfsgnA6SQlGvc3TVy8xZY" source=console
time="2025-06-13T21:27:24+05:30" level=info msg="IU-JP3FM8,2010,eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySUQiOiJJVS1KUDNGTTgiLCJVc2VyUm9sZSI6IklVIiwiT3ZlcnNlZVVzZXJJRCI6IkFHLU9LSURBNiIsIlZpZXdPbmx5IjpmYWxzZSwiR2VuZXJhdGVkRm9yIjoiQUctT0tJREE2IiwiZXhwIjoxNzUwMDAzMDQzLCJpc3MiOiJUZXN0TmFtZSJ9.webmwB_ui-W_cqQVTY0DVYHlFZlH8q97d0rCyIom1I4" source=console
time="2025-06-13T21:27:24+05:30" level=info msg="IU-BQ569Y,8565,eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySUQiOiJJVS1CUTU2OVkiLCJVc2VyUm9sZSI6IklVIiwiT3ZlcnNlZVVzZXJJRCI6IkFHLU9LSURBNiIsIlZpZXdPbmx5IjpmYWxzZSwiR2VuZXJhdGVkRm9yIjoiQUctT0tJREE2IiwiZXhwIjoxNzUwMDAzMDQzLCJpc3MiOiJUZXN0TmFtZSJ9.ZPSBniJ8EkUJG9UiC_g1DeIMOI8xsDHQGyyF7kcqdq0" source=console
time="2025-06-13T21:27:24+05:30" level=info msg="IU-OZPIJ0,0458,eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySUQiOiJJVS1PWlBJSjAiLCJVc2VyUm9sZSI6IklVIiwiT3ZlcnNlZVVzZXJJRCI6IkFHLU9LSURBNiIsIlZpZXdPbmx5IjpmYWxzZSwiR2VuZXJhdGVkRm9yIjoiQUctT0tJREE2IiwiZXhwIjoxNzUwMDAzMDQ0LCJpc3MiOiJUZXN0TmFtZSJ9.lLqux4z8bt8n8aDuQhtW7toY-Yh3Q7XrAn44uINH4Jg" source=console
time="2025-06-13T21:27:24+05:30" level=info msg="IU-U3SHG8,0452,eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySUQiOiJJVS1VM1NIRzgiLCJVc2VyUm9sZSI6IklVIiwiT3ZlcnNlZVVzZXJJRCI6IkFHLU9LSURBNiIsIlZpZXdPbmx5IjpmYWxzZSwiR2VuZXJhdGVkRm9yIjoiQUctT0tJREE2IiwiZXhwIjoxNzUwMDAzMDQ0LCJpc3MiOiJUZXN0TmFtZSJ9.DpK527XJDNg6qHy1NdRR2GWYGRKYvaF4mX92ST26xzE" source=console


  █ TOTAL RESULTS 

    HTTP
    http_req_duration.......................................................: avg=510.75ms min=455.71ms med=477.83ms max=737.73ms p(90)=592.64ms p(95)=665.19ms
      { expected_response:true }............................................: avg=510.75ms min=455.71ms med=477.83ms max=737.73ms p(90)=592.64ms p(95)=665.19ms
    http_req_failed.........................................................: 0.00%  0 out of 7
    http_reqs...............................................................: 7      1.85169/s

    EXECUTION
    iteration_duration......................................................: avg=3.78s    min=3.78s    med=3.78s    max=3.78s    p(90)=3.78s    p(95)=3.78s   
    iterations..............................................................: 1      0.264527/s
    vus.....................................................................: 1      min=1      max=1
    vus_max.................................................................: 1      min=1      max=1

    NETWORK
    data_received...........................................................: 8.7 kB 2.3 kB/s
    data_sent...............................................................: 1.3 kB 350 B/s




running (00m03.8s), 0/1 VUs, 1 complete and 0 interrupted iterations
default ✓ [ 100% ] 1 VUs  00m03.8s/10m0s  1/1 shared iters
