import http from 'k6/http';
import { check } from 'k6';
import { ADMIN_BASE,ADMIN_AUTH_TOKEN,OVERSEE_USER_ID } from '../config/config.js';
import { randomString } from '../utils/helpers.js';



export const options = {
  vus: 2,
  iterations: 2,
};

export default function createUser () {
  const index = __VU; // Unique per VU
  const firstName = 'test';
  const lastName = `${index}`;
  const email = `testuser_${randomString(6)}_${index}@example.com`;

  const payload = JSON.stringify({
    first_name: firstName,
    last_name: lastName,
    email: email,
    user_role: 'IU',
    oversee_user: OVERSEE_USER_ID,
  });

  const headers = {
  'Content-Type': 'application/json',
  Authorization: ADMIN_AUTH_TOKEN,
};

  const res = http.post(`${ADMIN_BASE}/user`, payload, { headers });

  check(res, {
    'createUser status is 200': (r) => r.status === 200,
    'createUser has user_id': (r) => JSON.parse(r.body).user_id !== undefined,
  });

  const response = JSON.parse(res.body);
  console.log('createUser response', response);

  console.log(JSON.stringify({
    user_id: response.user_id,
    password: response.password,
    email: response.email,
  }));
}