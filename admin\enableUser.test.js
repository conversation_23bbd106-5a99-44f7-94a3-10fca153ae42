import http from 'k6/http';
import { check, group, sleep } from 'k6';
import * as cfg from '../config/config.js'; // Expects ADMIN_BASE, ADMIN_AUTH_TOKEN, EXCHANGE, OVERSEE_USER_ID, USER_ID
import { SharedArray } from 'k6/data';
import Papa from 'https://jslib.k6.io/papaparse/5.1.1/index.js';

const users = new SharedArray('users', () => {
  const raw = open('../data/users.csv'); // adjust path as needed
  return Papa.parse(raw, { header: true }).data.filter(u => u.user_id);
});

const headers = {
  accept: 'application/json',
  'Content-Type': 'application/json',
  Authorization: cfg.ADMIN_AUTH_TOKEN,
};

export const options = {
  scenarios: {
    user_mapping_positive: {
      executor: 'per-vu-iterations',
      vus: 1,
      iterations: 1,
      exec: 'userMappingGoodFlow',
    },
  },
};

export function userMappingGoodFlow() {
    const user = users[__VU - 1];
  group('✅ User Mapping Full Flow – POSITIVE', () => {
    // 1. Get instruments
    const instRes = http.get(`${cfg.ADMIN_BASE}/userzinstruments/${cfg.EXCHANGE}?userId=${user.user_id}&page=1&page_size=5`, { headers });
    const instOk = check(instRes, { 'Get Instruments → 200': r => r.status === 200 });
    if (!instOk) {
      console.error('Get Instruments Failed:', instRes.status, instRes.body);
      return;
    }

    const instruments = instRes.json()?.rows?.slice(0, 2) ?? [];
    if (!instruments.length) {
      console.error('No instruments found');
      return;
    }

    // 2. Token mapping
    const stocks = instruments.map(i => ({
      id: i.financial_instrument_id,
      name: i.trading_symbol,
      instrument_type: i.instrument_type,
      exchange: i.exchange,
      margin: 1
    }));

    const tokenMapRes = http.post(`${cfg.ADMIN_BASE}/user-token-mapping`, JSON.stringify({
      user_id: user.user_id,
      exchange: cfg.EXCHANGE,
      segment: cfg.EXCHANGE,
      stocks,
    }), { headers });

    const tokenMapOk = check(tokenMapRes, {
      'Token Mapping → 200': r => r.status === 200,
      'Token Mapping → Success Msg': r => r.json()?.message?.includes('Updated'),
    });

    if (!tokenMapOk) {
      console.error('Token Mapping Failed:', tokenMapRes.status, tokenMapRes.body);
      return;
    }

    // 3. Global margin update
    const marginRes = http.post(`${cfg.ADMIN_BASE}/global-margin-update`, JSON.stringify({
      user_id: user.user_id,
      exchange: cfg.EXCHANGE,
      segment:  cfg.EXCHANGE,
      global_margin: '0.01',
    }), { headers });

    const marginOk = check(marginRes, {
      'Global Margin Update → 200': r => r.status === 200,
      'Global Margin → Success': r => r.json()?.message?.includes('updated'),
    });

    if (!marginOk) {
      console.error('Global Margin Failed:', marginRes.status, marginRes.body);
      return;
    }

    // 4. Agent wallet check
    const agentWalletRes = http.get(`${cfg.ADMIN_BASE}/wallets/balance/${cfg.OVERSEE_USER_ID}`, { headers });
    const agentWalletOk = check(agentWalletRes, { 'Agent Wallet → 200': r => r.status === 200 });
    if (!agentWalletOk) {
      console.error('Agent Wallet Failed:', agentWalletRes.status, agentWalletRes.body);
      return;
    }

    const available = agentWalletRes.json()?.wallets?.find(w => w.exchange === cfg.EXCHANGE)?.balance || 0;
    const applyAmount = 10000;

    if (available < applyAmount) {
      console.error(`Agent doesn't have enough balance → Available: ${available}, Needed: ${applyAmount}`);
      return;
    }

    // 5. Apply balance
    const applyRes = http.post(`${cfg.ADMIN_BASE}/user/applyBalance`, JSON.stringify({
      user_id: user.user_id,
      exchange: cfg.EXCHANGE,
      balance: `${applyAmount}`,
      oversee_id: cfg.OVERSEE_USER_ID,
      updated_by: cfg.OVERSEE_USER_ID,
    }), { headers });

    const applyOk = check(applyRes, {
      'Apply Balance → 200': r => r.status === 200,
      'Apply Balance → Message OK': r => r.json()?.message?.includes('balances updated'),
    });
    console.log(applyRes.json());

    if (!applyOk) {
      console.error('Apply Balance Failed:', applyRes.status, applyRes.body);
      return;
    }

    // 6. Confirm user wallet
    const userWalletRes = http.get(`${cfg.ADMIN_BASE}/wallets/balance/${user.user_id}`, { headers });
    const userWalletOk = check(userWalletRes, {
      'User Wallet After → 200': r => r.status === 200,
      'User Wallet → Updated': r => {
        const bal = userWalletRes.json()?.wallets?.find(w => w.exchange === cfg.EXCHANGE)?.balance || 0;
        return bal >= applyAmount;
      }
    });

    if (!userWalletOk) {
      console.error('User Wallet Check Failed:', userWalletRes.status, userWalletRes.body);
    }

    sleep(1);
  });
}
