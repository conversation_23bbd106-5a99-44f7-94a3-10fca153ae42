import http from 'k6/http';
import { check, group, sleep } from 'k6';
import * as cfg from '../config/config.js';
import { SharedArray } from 'k6/data';
import Papa from 'https://jslib.k6.io/papaparse/5.1.1/index.js';

const users = new SharedArray('users', () => {
  const raw = open('../data/users.csv');
  return Papa.parse(raw, { header: true }).data.filter(u => u.user_id);
});

const BAD_JWT = 'Bearer invalid.token.value';

const headers = (token) => ({
  accept: 'application/json',
  'Content-Type': 'application/json',
  Authorization: token,
});

export const options = {
  scenarios: {
    user_mapping_negative: {
      executor: 'per-vu-iterations',
      vus: 1,
      iterations: 1,
      exec: 'userMappingNegativeFlow',
    },
  },
};

export function userMappingNegativeFlow() {
  const user = users[__VU - 1];

  group('❌ NEGATIVE: Get Instruments API', () => {
    const tests = [
      { label: 'enablingUserNegativeFlow Get Instruments No JWT', url: `${cfg.ADMIN_BASE}/userzinstruments/${cfg.EXCHANGE}?userId=${user.user_id}`, hdr: headers(''), exp: 403 },
      { label: 'enablingUserNegativeFlow Get Instruments Bad JWT', url: `${cfg.ADMIN_BASE}/userzinstruments/${cfg.EXCHANGE}?userId=${user.user_id}`, hdr: headers(BAD_JWT), exp: 403 },
      { label: 'enablingUserNegativeFlow Get Instruments Invalid UserID', url: `${cfg.ADMIN_BASE}/userzinstruments/${cfg.EXCHANGE}?userId=FAKE_ID`, hdr: headers(cfg.ADMIN_AUTH_TOKEN), exp: 404 },
      { label: 'enablingUserNegativeFlow Get Instruments Bad Exchange', url: `${cfg.ADMIN_BASE}/userzinstruments/INVALID?userId=${user.user_id}`, hdr: headers(cfg.ADMIN_AUTH_TOKEN), exp: 400 },
    ];

    tests.forEach(({ label, url, hdr, exp }) => {
      const res = http.get(url, { headers: hdr });
      const ok = check(res, { [`${label} → ${exp}`]: r => r.status === exp });
      if (!ok) logError(label, res);
    });
  });

  group('❌ NEGATIVE: Token Mapping API', () => {
    const baseBody = {
      user_id: user.user_id,
      exchange: cfg.EXCHANGE,
      segment: cfg.EXCHANGE,
      stocks: [{ id: 999999, name: 'Fake', instrument_type: 'EQ', exchange: cfg.EXCHANGE, margin: 1 }],
    };

    const tests = [
      { label: 'enablingUserNegativeFlow Token Mapping No JWT', token: '', body: baseBody, exp: 403 },
      { label: 'enablingUserNegativeFlow Token Mapping Bad JWT', token: BAD_JWT, body: baseBody, exp: 403 },
      { label: 'enablingUserNegativeFlow Token Mapping Missing stocks', token: cfg.ADMIN_AUTH_TOKEN, body: { ...baseBody, stocks: undefined }, exp: 422 },
      { label: 'enablingUserNegativeFlow Token Mapping Empty stocks array', token: cfg.ADMIN_AUTH_TOKEN, body: { ...baseBody, stocks: [] }, exp: 200 },
      { label: 'enablingUserNegativeFlow Token Mapping Invalid user ID', token: cfg.ADMIN_AUTH_TOKEN, body: { ...baseBody, user_id: 'FAKE_USER' }, exp: 500 },
    ];

    tests.forEach(({ label, token, body, exp }) => {
      const res = http.post(`${cfg.ADMIN_BASE}/user-token-mapping`, JSON.stringify(body), { headers: headers(token) });
      const ok = check(res, { [`${label} → ${exp}`]: r => r.status === exp });
      if (!ok) logError(label, res, body);
    });
  });

  group('❌ NEGATIVE: Global Margin Update', () => {
    const base = {
      user_id: user.user_id,
      exchange: 'CRYPTO',
      segment: 'CRYPTO',
    };

    const tests = [
      { label: 'enablingUserNegativeFlow Global Margin Missing margin', body: { ...base }, exp: 422 },
      { label: 'enablingUserNegativeFlow Global Margin String margin', body: { ...base, global_margin: 'abc' }, exp: 422 },
      { label: 'enablingUserNegativeFlow Global Margin Negative margin', body: { ...base, global_margin: '-1' }, exp: 200 },
      { label: 'enablingUserNegativeFlow Global Margin Huge margin', body: { ...base, global_margin: '9999999999' }, exp: 200 },
      { label: 'enablingUserNegativeFlow Global Margin Bad user ID', body: { ...base, user_id: 'FAKE_ID', global_margin: '2' }, exp: 500 },
      { label: 'enablingUserNegativeFlow Global Margin Bad JWT', body: { ...base, global_margin: '2' }, token: BAD_JWT, exp: 403 },
    ];

    tests.forEach(({ label, body, exp, token = cfg.ADMIN_AUTH_TOKEN }) => {
      const res = http.post(`${cfg.ADMIN_BASE}/global-margin-update`, JSON.stringify(body), { headers: headers(token) });
      const ok = check(res, { [`${label} → ${exp}`]: r => r.status === exp });
      if (!ok) logError(label, res, body);
    });
  });

  group('❌ NEGATIVE: Apply Balance API', () => {
    const base = {
      user_id: user.user_id,
      exchange: cfg.EXCHANGE,
      oversee_id: cfg.OVERSEE_USER_ID,
      updated_by: cfg.OVERSEE_USER_ID,
    };

    const tests = [
      { label: 'enablingUserNegativeFlow Apply Balance Missing balance', body: { ...base }, exp: 422 },
      { label: 'enablingUserNegativeFlow Apply Balance Negative balance', body: { ...base, balance: '-100' }, exp: 200 },
      { label: 'enablingUserNegativeFlow Apply Balance Non-numeric balance', body: { ...base, balance: 'abc' }, exp: 422 },
      { label: 'enablingUserNegativeFlow Apply Balance Huge balance', body: { ...base, balance: '99999999999' }, exp: 400 },
      { label: 'enablingUserNegativeFlow Apply Balance Bad user ID', body: { ...base, balance: '10000', user_id: 'FAKE_ID' }, exp: 404 },
      { label: 'enablingUserNegativeFlow Apply Balance Bad JWT', body: { ...base, balance: '10000' }, token: BAD_JWT, exp: 403 },
    ];

    tests.forEach(({ label, body, exp, token = cfg.ADMIN_AUTH_TOKEN }) => {
      const res = http.post(`${cfg.ADMIN_BASE}/user/applyBalance`, JSON.stringify(body), { headers: headers(token) });
      const ok = check(res, { [`${label} → ${exp}`]: r => r.status === exp });
      if (!ok) logError(label, res, body);
    });
  });

  group('❌ NEGATIVE: User Wallet API', () => {
    const tests = [
      { label: 'enablingUserNegativeFlow User Wallet Bad JWT', url: `${cfg.ADMIN_BASE}/wallets/balance/${user.user_id}`, token: BAD_JWT, exp: 403 },
      { label: 'enablingUserNegativeFlow User Wallet No JWT', url: `${cfg.ADMIN_BASE}/wallets/balance/${user.user_id}`, token: '', exp: 403 },
      { label: 'enablingUserNegativeFlow User Wallet Invalid user ID', url: `${cfg.ADMIN_BASE}/wallets/balance/FAKE_ID`, token: cfg.ADMIN_AUTH_TOKEN, exp: 404 },
    ];

    tests.forEach(({ label, url, token, exp }) => {
      const res = http.get(url, { headers: headers(token) });
      const ok = check(res, { [`${label} → ${exp}`]: r => r.status === exp });
      if (!ok) logError(label, res);
    });
  });

  sleep(1);
}

function logError(label, res, body = null) {
  console.error(`❌ [${label}] Expected status mismatch`);
  if (body) console.error('Payload:', JSON.stringify(body, null, 2));
  console.error('Response:', res.status, res.body);
}
