import http from 'k6/http';
import { check, group, sleep } from 'k6';
import * as cfg from '../config/config.js';

function headers(token) {
  return {
    'Content-Type': 'application/json',
    Authorization: token,
  };
}

export default function () {
  group('🔐 Admin Password Update Flow', () => {
    testPasswordChangeByUserId();
    testPasswordChangeByEmail();
  });
}

// ---------------------------------------------
// ✅ USER ID BASED PASSWORD CHANGE
// ---------------------------------------------
function testPasswordChangeByUserId() {
  group('Update Password - Using UserID', () => {
    const hdrs = headers(cfg.ADMIN_AUTH_TOKEN);

    // 1. Change to temp password
    let res = http.post(`${cfg.ADMIN_BASE}/update-password-userid`, JSON.stringify({
      userId: cfg.OVERSEE_USER_ID,
      current_password: cfg.ADMIN_PASSWORD,
      new_password: 'Temp123456',
    }), { headers: hdrs });

    if (check(res, { 'adminUpdatePassword 1st time update → 200': (r) => r.status === 200 })) {
      console.log('adminUpdatePassword updated successfully (userId)');
    } else {
      console.error(`adminUpdatePassword Failed to change password (userId). ${res.status}: ${res.body}`);
    }

    // 2. Revert to original
    res = http.post(`${cfg.ADMIN_BASE}/update-password-userid`, JSON.stringify({
      userId: cfg.OVERSEE_USER_ID,
      current_password: 'Temp123456',
      new_password: cfg.ADMIN_PASSWORD,
    }), { headers: hdrs });

    if (check(res, { 'adminUpdatePassword revert password → 200': (r) => r.status === 200 })) {
      console.log('adminUpdatePassword Password reverted (userId)');
    } else {
      console.error(`adminUpdatePassword Failed to revert password. ${res.status}: ${res.body}`);
    }

    // 3. ❌ Invalid current password
    res = http.post(`${cfg.ADMIN_BASE}/update-password-userid`, JSON.stringify({
      userId: cfg.OVERSEE_USER_ID,
      current_password: 'WrongPass123',
      new_password: cfg.ADMIN_PASSWORD,
    }), { headers: hdrs });

    if (check(res, { 'adminUpdatePassword invalid current password → 400+': (r) => r.status >= 400 })) {
      console.log(`adminUpdatePassword Expected error: ${res.status} → ${res.json().message || 'Invalid current password'}`);
    } else {
      console.error(`adminUpdatePassword Unexpected success or response: ${res.status} → ${res.body}`);
    }
  });
}

// ---------------------------------------------
// ✅ EMAIL BASED PASSWORD CHANGE
// ---------------------------------------------
function testPasswordChangeByEmail() {
  group('adminUpdatePassword Update Password - Using Email', () => {
    const hdrs = headers(cfg.ADMIN_AUTH_TOKEN);

    // 1. Change to another temp password
    let res = http.post(`${cfg.ADMIN_BASE}/update-password`, JSON.stringify({
      email: cfg.ADMIN_EMAIL,
      current_password: cfg.ADMIN_PASSWORD,
      new_password: 'NewPass7890',
    }), { headers: hdrs });

    if (check(res, { 'adminUpdatePassword change password (email) → 200': (r) => r.status === 200 })) {
      console.log('adminUpdatePassword Password changed via email');
    } else {
      console.error(`adminUpdatePassword Failed to change password via email. ${res.status}: ${res.body}`);
    }

    // 2. Revert
    res = http.post(`${cfg.ADMIN_BASE}/update-password`, JSON.stringify({
      email: cfg.ADMIN_EMAIL,
      current_password: 'NewPass7890',
      new_password: cfg.ADMIN_PASSWORD,
    }), { headers: hdrs });

    if (check(res, { 'adminUpdatePassword revert password (email) → 200': (r) => r.status === 200 })) {
      console.log('adminUpdatePassword Password reverted via email');
    } else {
      console.error(`adminUpdatePassword Failed to revert password via email. ${res.status}: ${res.body}`);
    }

    // 3. ❌ Invalid password
    res = http.post(`${cfg.ADMIN_BASE}/update-password`, JSON.stringify({
      email: cfg.ADMIN_EMAIL,
      current_password: 'Invalid123',
      new_password: 'short',
    }), { headers: hdrs });

    if (check(res, {'adminUpdatePassword invalid current password → 400+': (r) => r.status >= 400 })) {
      console.log(`adminUpdatePassword Caught error as expected: ${res.status} → ${res.json().message || 'Validation error'}`);
    } else {
      console.error(`adminUpdatePassword Unexpected success with invalid data. ${res.status}: ${res.body}`);
    }
  });

  sleep(1);
}
