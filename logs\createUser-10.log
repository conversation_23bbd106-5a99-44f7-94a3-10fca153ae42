
         /\      Grafana   /‾‾/  
    /\  /  \     |\  __   /  /   
   /  \/    \    | |/ /  /   ‾‾\ 
  /          \   |   (  |  (‾)  |
 / __________ \  |_|\_\  \_____/ 

     execution: local
        script: C:\Users\<USER>\Documents\k6\k6\scripts\create-users.dynamic.js
        output: -

     scenarios: (100.00%) 1 scenario, 10 max VUs, 10m30s max duration (incl. graceful stop):
              * default: 10 iterations shared among 10 VUs (maxDuration: 10m0s, gracefulStop: 30s)


running (00m01.0s), 10/10 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 10 VUs  00m01.0s/10m0s  00/10 shared iters

running (00m02.0s), 10/10 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 10 VUs  00m02.0s/10m0s  00/10 shared iters

running (00m03.0s), 10/10 VUs, 0 complete and 0 interrupted iterations
default   [   0% ] 10 VUs  00m03.0s/10m0s  00/10 shared iters
time="2025-06-17T16:45:45+05:30" level=info msg="createUser response {\"detail\":\"User limit error\"}" source=console
time="2025-06-17T16:45:45+05:30" level=info msg="{}" source=console
time="2025-06-17T16:45:45+05:30" level=info msg="createUser response {\"detail\":\"User limit error\"}" source=console
time="2025-06-17T16:45:45+05:30" level=info msg="createUser response {\"detail\":\"User limit error\"}" source=console
time="2025-06-17T16:45:45+05:30" level=info msg="{}" source=console
time="2025-06-17T16:45:45+05:30" level=info msg="createUser response {\"detail\":\"User limit error\"}" source=console
time="2025-06-17T16:45:45+05:30" level=info msg="{}" source=console
time="2025-06-17T16:45:45+05:30" level=info msg="{}" source=console

running (00m04.0s), 06/10 VUs, 4 complete and 0 interrupted iterations
default   [  40% ] 10 VUs  00m04.0s/10m0s  04/10 shared iters
time="2025-06-17T16:45:46+05:30" level=info msg="createUser response {\"detail\":\"User limit error\"}" source=console
time="2025-06-17T16:45:46+05:30" level=info msg="createUser response {\"detail\":\"User limit error\"}" source=console
time="2025-06-17T16:45:46+05:30" level=info msg="createUser response {\"detail\":\"User limit error\"}" source=console
time="2025-06-17T16:45:46+05:30" level=info msg="{}" source=console
time="2025-06-17T16:45:46+05:30" level=info msg="{}" source=console
time="2025-06-17T16:45:46+05:30" level=info msg="createUser response {\"detail\":\"User limit error\"}" source=console
time="2025-06-17T16:45:46+05:30" level=info msg="{}" source=console
time="2025-06-17T16:45:46+05:30" level=info msg="createUser response {\"detail\":\"User limit error\"}" source=console
time="2025-06-17T16:45:46+05:30" level=info msg="{}" source=console
time="2025-06-17T16:45:46+05:30" level=info msg="createUser response {\"detail\":\"User limit error\"}" source=console
time="2025-06-17T16:45:46+05:30" level=info msg="{}" source=console
time="2025-06-17T16:45:46+05:30" level=info msg="{}" source=console


  █ TOTAL RESULTS 

    checks_total.......................: 20      4.539345/s
    checks_succeeded...................: 0.00%   0 out of 20
    checks_failed......................: 100.00% 20 out of 20

    ✗ createUser status is 200
      ↳  0% — ✓ 0 / ✗ 10
    ✗ createUser has user_id
      ↳  0% — ✓ 0 / ✗ 10

    HTTP
    http_req_duration......................: avg=3.56s min=3.28s med=3.7s max=3.78s p(90)=3.78s p(95)=3.78s
    http_req_failed........................: 100.00% 10 out of 10
    http_reqs..............................: 10      2.269673/s

    EXECUTION
    iteration_duration.....................: avg=4.2s  min=3.91s med=4.4s max=4.4s  p(90)=4.4s  p(95)=4.4s 
    iterations.............................: 10      2.269673/s
    vus....................................: 6       min=6        max=10
    vus_max................................: 10      min=10       max=10

    NETWORK
    data_received..........................: 37 kB   8.4 kB/s
    data_sent..............................: 9.4 kB  2.1 kB/s




running (00m04.4s), 00/10 VUs, 10 complete and 0 interrupted iterations
default ✓ [ 100% ] 10 VUs  00m04.4s/10m0s  10/10 shared iters
