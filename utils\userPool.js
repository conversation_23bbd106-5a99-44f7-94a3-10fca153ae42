import { SharedArray } from 'k6/data';
import <PERSON> from 'https://jslib.k6.io/papaparse/5.1.1/index.js';

// Read and parse the CSV file
export const users = new SharedArray('users', () => {
  try {
    const raw = open('./data/users_with_tokens.csv');

    if (!raw) {
      console.error('❌ Failed to read CSV file: ./data/users_with_tokens.csv');
      return [];
    }

    // Fix tokens: join lines by merging broken JWTs
    const fixed = raw
      .split('\n')
      .reduce((acc, line) => {
        // Start of a new row (has 3 columns)
        if ((line.match(/,/g) || []).length === 2) {
          acc.push(line);
        } else if (acc.length > 0) {
          // Append broken token part to the last line
          acc[acc.length - 1] += line.trim();
        }
        return acc;
      }, [])
      .join('\n');

    // Parse cleaned CSV and strip whitespace from tokens
    const parsedUsers = Papa.parse(fixed, { header: true }).data
      .filter(u => u.user_id && u.token)
      .map(u => ({
        ...u,
        token: u.token.replace(/\s/g, ''), // remove all whitespace (linebreaks, spaces, tabs)
      }));

    console.log(`✅ Loaded ${parsedUsers.length} users from CSV`);
    return parsedUsers;
  } catch (error) {
    console.error('❌ Error loading users:', error);
    return [];
  }
});

// Provide one user per VU
export function getUser() {
  if (!users || users.length === 0) {
    console.error('❌ No users available. Check CSV file loading.');
    return null;
  }

  const userIndex = (__VU - 1) % users.length;
  const user = users[userIndex];

  if (!user) {
    console.error(`❌ No user found at index ${userIndex}. VU: ${__VU}, Total users: ${users.length}`);
    return null;
  }

  console.log(`✅ Using user: ${user.user_id} (VU: ${__VU})`);
  return user;
}

// Just return cleaned token from CSV (no login)
export function getToken(user_id, _password) {
  const user = users.find(u => u.user_id === user_id);
  const token = user?.token?.replace(/\s/g, '');
  if (!token) {
    console.error(`❌ No token found for ${user_id}`);
  }
  return token || null;
}
