const express = require('express');
const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');

const router = express.Router();

// Configuration
const CONFIG = {
  logsDir: path.join(__dirname, '../logs'),
  userMainFile: path.join(__dirname, '../scenarios/main.test.js'),
  configFile: path.join(__dirname, '../config/config.js')
};

// Ensure logs directory exists
if (!fs.existsSync(CONFIG.logsDir)) {
  fs.mkdirSync(CONFIG.logsDir, { recursive: true });
}

/**
 * Load configuration from config.js
 */
function loadConfig() {
  try {
    // Read config file content
    const configContent = fs.readFileSync(CONFIG.configFile, 'utf8');
    
    // Extract RAMP_CONFIG using regex (since it's a JS file, not JSON)
    const rampConfigMatch = configContent.match(/"RAMP_CONFIG":\s*({[\s\S]*?})\s*[,}]/);
    
    if (rampConfigMatch) {
      const rampConfigStr = rampConfigMatch[1];
      const rampConfig = JSON.parse(rampConfigStr);
      return { RAMP_CONFIG: rampConfig };
    }
    
    // Fallback default config
    return {
      RAMP_CONFIG: {
        executor: "ramping-vus",
        startVUs: 0,
        stages: [
          { duration: "30s", target: 2 },
          { duration: "1m", target: 2 },
          { duration: "30s", target: 0 }
        ],
        gracefulStop: "30s"
      }
    };
  } catch (error) {
    console.error('❌ Error loading config:', error);
    // Return default config
    return {
      RAMP_CONFIG: {
        executor: "ramping-vus",
        startVUs: 0,
        stages: [
          { duration: "30s", target: 2 },
          { duration: "1m", target: 2 },
          { duration: "30s", target: 0 }
        ],
        gracefulStop: "30s"
      }
    };
  }
}

/**
 * POST /api/run-k6
 * Enhanced K6 test runner with scenario vs load testing support
 */
router.post('/run-k6', async (req, res) => {
  const { 
    scenarios, 
    testMode = "scenario", // "scenario" or "load"
    loadConfig = null,     // Optional custom load config
    staticAuth = null      // Optional static auth for scenario mode
  } = req.body;

  // Validation
  if (!Array.isArray(scenarios) || scenarios.length === 0) {
    return res.status(400).json({ 
      error: "body.scenarios must be a non-empty array" 
    });
  }

  if (!["scenario", "load"].includes(testMode)) {
    return res.status(400).json({ 
      error: "testMode must be 'scenario' or 'load'" 
    });
  }

  try {
    // Prepare environment variables
    const env = {
      ...process.env,
      SCENARIOS: scenarios.join(","),
      TEST_MODE: testMode,
    };

    // Add static auth for scenario mode
    if (testMode === "scenario" && staticAuth) {
      env.USER_ID = staticAuth.user_id || env.USER_ID;
      env.AUTH_TOKEN = staticAuth.auth_token || env.AUTH_TOKEN;
    }

    // For load testing, apply load configuration
    let k6Options = '';
    if (testMode === "load") {
      const config = loadConfig || loadConfig();
      const rampConfig = config.RAMP_CONFIG;
      
      // Convert config to K6 options
      k6Options = `--vus ${rampConfig.startVUs || 0}`;
      
      if (rampConfig.stages && rampConfig.stages.length > 0) {
        const stagesStr = rampConfig.stages
          .map(stage => `${stage.duration}:${stage.target}`)
          .join(',');
        k6Options += ` --stage ${stagesStr}`;
      }
      
      if (rampConfig.gracefulStop) {
        k6Options += ` --grace-period ${rampConfig.gracefulStop}`;
      }

      console.log(`🚀 Running load test with config:`, rampConfig);
    } else {
      console.log(`🧪 Running scenario test for: ${scenarios.join(', ')}`);
    }

    // Generate log file name
    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
    const label = scenarios.join("+").substring(0, 60).replace(/[^a-zA-Z0-9+_-]/g, "");
    const logFile = path.join(CONFIG.logsDir, `${testMode}-${label}-${timestamp}.txt`);
    
    // Build K6 command
    const k6Cmd = `k6 run ${k6Options} "${CONFIG.userMainFile}" > "${logFile}" 2>&1`;
    
    console.log(`📝 Executing: ${k6Cmd}`);
    console.log(`📄 Log file: ${logFile}`);

    // Execute K6 test
    exec(k6Cmd, { env }, (err, stdout, stderr) => {
      const logFileName = path.basename(logFile, ".txt");
      
      if (err) {
        console.error('❌ K6 execution failed:', err);
        return res.status(500).json({ 
          error: "k6 run failed",
          details: err.message,
          logFile: logFileName,
          command: k6Cmd
        });
      }

      console.log('✅ K6 test completed successfully');
      
      // Try to read basic results from log file
      let summary = null;
      try {
        if (fs.existsSync(logFile)) {
          const logContent = fs.readFileSync(logFile, 'utf8');
          
          // Extract basic metrics from log
          const checksMatch = logContent.match(/checks[.\s]*:\s*(\d+\.\d+%)\s*(\d+)\s*out\s*of\s*(\d+)/);
          const iterationsMatch = logContent.match(/iterations[.\s]*:\s*(\d+)/);
          const httpReqsMatch = logContent.match(/http_reqs[.\s]*:\s*(\d+)/);
          
          if (checksMatch || iterationsMatch || httpReqsMatch) {
            summary = {
              checks: checksMatch ? {
                rate: checksMatch[1],
                passed: parseInt(checksMatch[2]),
                total: parseInt(checksMatch[3])
              } : null,
              iterations: iterationsMatch ? parseInt(iterationsMatch[1]) : null,
              httpRequests: httpReqsMatch ? parseInt(httpReqsMatch[1]) : null
            };
          }
        }
      } catch (parseError) {
        console.warn('⚠️ Could not parse test results:', parseError.message);
      }

      res.json({ 
        success: true, 
        message: `${testMode} test completed successfully`,
        testMode,
        scenarios,
        logFile: logFileName,
        summary,
        config: testMode === "load" ? loadConfig : null
      });
    });

  } catch (error) {
    console.error('❌ API error:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
});

/**
 * GET /api/test-config
 * Get current test configuration
 */
router.get('/test-config', (req, res) => {
  try {
    const config = loadConfig();
    
    res.json({
      success: true,
      config,
      modes: {
        scenario: {
          description: "Single scenario testing with static auth",
          usesCsv: false,
          authSource: "config file or API parameters"
        },
        load: {
          description: "Load testing with multiple users from CSV",
          usesCsv: true,
          authSource: "CSV file with user tokens"
        }
      }
    });
  } catch (error) {
    console.error('❌ Error getting config:', error);
    res.status(500).json({
      error: 'Failed to load configuration',
      details: error.message
    });
  }
});

/**
 * GET /api/logs/:filename
 * Get specific log file content
 */
router.get('/logs/:filename', (req, res) => {
  try {
    const filename = req.params.filename;
    const logFile = path.join(CONFIG.logsDir, `${filename}.txt`);
    
    if (!fs.existsSync(logFile)) {
      return res.status(404).json({
        error: 'Log file not found',
        filename: filename
      });
    }

    const content = fs.readFileSync(logFile, 'utf8');
    
    res.json({
      success: true,
      filename: filename,
      content: content,
      size: content.length
    });
    
  } catch (error) {
    console.error('❌ Error reading log file:', error);
    res.status(500).json({
      error: 'Failed to read log file',
      details: error.message
    });
  }
});

/**
 * GET /api/logs
 * List all log files
 */
router.get('/logs', (req, res) => {
  try {
    const files = fs.readdirSync(CONFIG.logsDir)
      .filter(file => file.endsWith('.txt'))
      .map(file => {
        const filePath = path.join(CONFIG.logsDir, file);
        const stats = fs.statSync(filePath);
        return {
          name: path.basename(file, '.txt'),
          fullName: file,
          size: stats.size,
          created: stats.birthtime,
          modified: stats.mtime
        };
      })
      .sort((a, b) => b.modified - a.modified); // Sort by newest first

    res.json({
      success: true,
      files: files,
      count: files.length
    });
    
  } catch (error) {
    console.error('❌ Error listing log files:', error);
    res.status(500).json({
      error: 'Failed to list log files',
      details: error.message
    });
  }
});

module.exports = router;
