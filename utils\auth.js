import http  from 'k6/http';
import { check } from 'k6';
import { BASE_URL } from '../config/config.js';
import { myUser }  from './userPool.js';

export function loginOnce () {
  const { user_id, password } = myUser();

  const res = http.post(
    `${BASE_URL}/api/auth/login`,
    JSON.stringify({ identifier: user_id, password }),
    { headers: { 'Content-Type': 'application/json' } }
  );

  check(res, { 'login ok': r => r.status === 200 });

  return res.json('token');           // adjust if your JSON path differs
}
