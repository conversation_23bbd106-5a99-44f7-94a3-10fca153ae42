# extract_users.ps1
$logPath   = "login_output.log"                 # UTF-16 LE file
$outputCsv = "data/users_with_tokens.csv"

# 1) Read ENTIRE file as one string (-Raw) and specify UTF-16 (Unicode)
$text = Get-Content -Path $logPath -Raw -Encoding Unicode

# 2) Find every msg=" … " blob.  The 'Singleline' option lets '.' match new-lines.
$regex    = 'msg="([^"]+)"\s+source=console'    # same pattern the Python demo used
$matches  = [regex]::Matches($text, $regex, 'Singleline')

# 3) Build the CSV rows
$rows = foreach ($m in $matches) {
    $payload = $m.Groups[1].Value -replace '[\r\n]'  # flatten embedded CR/LF
    if ($payload -eq 'user_id,password,token') { continue } # skip header copies
    if ($payload -notmatch '^IU-')            { continue } # keep only real data
    $payload
}

# 4) Inject header and write out
@('user_id,password,token') + $rows |
    Out-File -FilePath $outputCsv -Encoding UTF8 -Force

Write-Host "`n✅  $($rows.Count) user records written to $outputCsv`n"
