import { sleep, check, group } from 'k6';
import http from 'k6/http';
import { SharedArray } from 'k6/data';
import { BASE_URL, ADMIN_BASE, EXCHANGE } from '../config/config.js';

// Parse the CSV and clean up broken tokens
const users = new SharedArray('users', () => {
    try {
        const raw = open('../data/users_with_tokens.csv');

        if (!raw) {
            console.error('❌ Failed to read CSV file: ./data/users_with_tokens.csv');
            return [];
        }

        const cleaned = raw
            .split('\n')
            .reduce((acc, line) => {
                if ((line.match(/,/g) || []).length === 2) {
                    acc.push(line);
                } else if (acc.length > 0) {
                    acc[acc.length - 1] += line.trim();
                }
                return acc;
            }, []);

        const parsedUsers = cleaned
            .slice(1) // skip header
            .filter(Boolean)
            .map(line => {
                const [user_id, password, token] = line.trim().split(',');
                return {
                    user_id: user_id?.trim(),
                    password: password?.trim(),
                    token: token?.replace(/\s/g, '') // strip all whitespace
                };
            })
            .filter(u => u.user_id && u.token); // Only keep valid users

        console.log(`✅ Loaded ${parsedUsers.length} users from CSV`);
        return parsedUsers;
    } catch (error) {
        console.error('❌ Error loading users from CSV:', error);
        return [];
    }
});

export const options = {
    vus: 1,
    iterations: 1,
};

// Safe JSON parsing
function safeJson(res) {
    try {
        const json = res.json();
        return typeof json === 'object' && json !== null ? json : {};
    } catch (e) {
        console.error(`❌ Failed to parse JSON [${res.status}]:`, res.body);
        return {};
    }
}

export default function buyGoodMarketScenario() {
    group('✅ Buy Market Order - Good Flow', () => {
        // Check if users array is loaded
        if (!users || users.length === 0) {
            console.error(`❌ buyGoodMarketScenario: No users loaded from CSV. Check file path and content.`);
            return;
        }

        const userIndex = (__VU - 1) % users.length;
        const user = users[userIndex];

        if (!user) {
            console.error(`❌ buyGoodMarketScenario: No user found at index ${userIndex} for VU ${__VU}. Total users: ${users.length}`);
            return;
        }

        const { user_id, token } = user;

        if (!user_id || !token) {
            console.error(`❌ buyGoodMarketScenario: Missing user_id or token for VU ${__VU}:`, user);
            return;
        }

        console.log(`✅ buyGoodMarketScenario: Using user: ${user_id} (VU: ${__VU})`);

        const headers = {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`,
        };

        // 1. Wallet BEFORE
        const walletBeforeRes = http.get(`${ADMIN_BASE}/wallets/balance/${user_id}`, { headers });
        const walletBeforeJson = safeJson(walletBeforeRes);

        const walletBeforeOk = check(walletBeforeRes, {
            'buyGoodMarketScenario GET wallet before → 200': (r) => r.status === 200,
            'buyGoodMarketScenario GET wallet before → has wallets': (r) => {
                try {
                    const json = r.json();
                    return Array.isArray(json.wallets) && json.wallets.length > 0;
                } catch (e) {
                    console.error('buyGoodMarketScenario GET wallet before → JSON parse error:', e.message);
                    return false;
                }
            },
        });

        if (!walletBeforeOk) {
            console.error(`buyGoodMarketScenario GET wallet before failed:`, walletBeforeRes.status, walletBeforeRes.body);
            return;
        }

        const netBefore = Array.isArray(walletBeforeJson.wallets)
            ? parseFloat(walletBeforeJson.wallets.find(w => w.exchange === EXCHANGE)?.net_balance || 0)
            : 0;

        if (netBefore <= 0) {
            console.error(`buyGoodMarketScenario: Insufficient balance (${netBefore}) for user ${user_id}`);
            return;
        }

        // 2. Get instruments
        const instPayload = JSON.stringify({ exchange: EXCHANGE, searchPattern: '', page: 1, pageSize: 10 });
        const instRes = http.post(`${BASE_URL}/api/user/getInstruments`, instPayload, { headers });
        const instJson = safeJson(instRes);

        const instOk = check(instRes, {
            'buyGoodMarketScenario POST getInstruments → 200': (r) => r.status === 200,
            'buyGoodMarketScenario POST getInstruments → has instruments': (r) => {
                try {
                    const json = r.json();
                    return Array.isArray(json.instruments) && json.instruments.length > 0;
                } catch (e) {
                    console.error('buyGoodMarketScenario POST getInstruments → JSON parse error:', e.message);
                    return false;
                }
            },
        });

        if (!instOk) {
            console.error(`buyGoodMarketScenario POST getInstruments failed:`, instRes.status, instRes.body);
            return;
        }

        // Select a random instrument from the array
        let selectedInstrument = null;
        if (Array.isArray(instJson.instruments) && instJson.instruments.length > 0) {
            const randomIndex = Math.floor(Math.random() * instJson.instruments.length);
            selectedInstrument = instJson.instruments[randomIndex];
            
            console.log(`Selected random instrument (${randomIndex+1}/${instJson.instruments.length}):
            ID: ${selectedInstrument.FinancialInstrumentID}
            Symbol: ${selectedInstrument.TradingSymbol || 'N/A'}
            Price: ${selectedInstrument.LastPrice || 'N/A'}`);
        }

        const instrumentId = selectedInstrument?.FinancialInstrumentID || null;

        if (!instrumentId) {
            console.error(`buyGoodMarketScenario: No instrument found for user ${user_id}`);
            return;
        }

        // 3. Place order
        const orderPayload = {
            instrumentId,
            exchange: EXCHANGE,
            quantity: 1,
            OrderType: 'MARKET',
            buy: true,
        };
        const orderRes = http.post(`${BASE_URL}/api/user/makeOrder`, JSON.stringify(orderPayload), { headers });
        const orderJson = safeJson(orderRes);

        const orderOk = check(orderRes, {
            'buyGoodMarketScenario POST makeOrder → 200': (r) => r.status === 200,
            'buyGoodMarketScenario POST makeOrder → success': (r) => {
                try {
                    const json = r.json();
                    return json.status === 'success';
                } catch (e) {
                    console.error('buyGoodMarketScenario POST makeOrder → JSON parse error:', e.message);
                    return false;
                }
            },
        });

        if (!orderOk) {
            console.error(`buyGoodMarketScenario POST makeOrder failed:`, orderRes.status, orderRes.body);
            return;
        }

        sleep(1);

        // 4. Wallet AFTER
        const walletAfterRes = http.get(`${ADMIN_BASE}/wallets/balance/${user_id}`, { headers });
        const walletAfterJson = safeJson(walletAfterRes);

        const walletAfterOk = check(walletAfterRes, {
            'buyGoodMarketScenario GET wallet after → 200': (r) => r.status === 200,
            'buyGoodMarketScenario GET wallet after → has wallets': (r) => {
                try {
                    const json = r.json();
                    return Array.isArray(json.wallets) && json.wallets.length > 0;
                } catch (e) {
                    console.error('buyGoodMarketScenario GET wallet after → JSON parse error:', e.message);
                    return false;
                }
            },
        });

        if (!walletAfterOk) {
            console.error(`buyGoodMarketScenario GET wallet after failed:`, walletAfterRes.status, walletAfterRes.body);
            return;
        }

        const netAfter = Array.isArray(walletAfterJson.wallets)
            ? parseFloat(walletAfterJson.wallets.find(w => w.exchange === EXCHANGE)?.net_balance || 0)
            : 0;

        const balanceOk = check({}, {
            'buyGoodMarketScenario → net_balance reduced after order': () => {
                const reduced = netAfter < netBefore;
                if (!reduced) {
                    console.error(`buyGoodMarketScenario: Balance not reduced. Before: ${netBefore}, After: ${netAfter}`);
                }
                return reduced;
            },
        });

        if (!balanceOk) {
            console.error(`buyGoodMarketScenario: Balance validation failed for user ${user_id}`);
        }

        // 5. Portfolio check
        const portRes = http.post(`${BASE_URL}/api/user/getPortfolio`, JSON.stringify({ exchange: EXCHANGE }), { headers });
        const portJson = safeJson(portRes);

        const portOk = check(portRes, {
            'buyGoodMarketScenario POST getPortfolio → 200': (r) => r.status === 200,
            'buyGoodMarketScenario POST getPortfolio → has portfolios': (r) => {
                try {
                    const json = r.json();
                    return Array.isArray(json.portfolios);
                } catch (e) {
                    console.error('buyGoodMarketScenario POST getPortfolio → JSON parse error:', e.message);
                    return false;
                }
            },
        });

        if (!portOk) {
            console.error(`buyGoodMarketScenario POST getPortfolio failed:`, portRes.status, portRes.body);
            return;
        }

        const inPortfolio = Array.isArray(portJson.portfolios)
            ? portJson.portfolios.some(p => p.FinancialInstrumentID === instrumentId)
            : false;

        const portfolioOk = check({}, {
            'buyGoodMarketScenario → portfolio includes instrument': () => {
                if (!inPortfolio) {
                    console.error(`buyGoodMarketScenario: Instrument ${instrumentId} not found in portfolio for user ${user_id}`);
                }
                return inPortfolio;
            },
        });

        if (!portfolioOk) {
            console.error(`buyGoodMarketScenario: Portfolio validation failed for user ${user_id}`);
        }

        sleep(1);
    });
}

export function buyBadMarketScenario() {
    group('❌ Buy Market Order - Bad Scenarios', () => {

        // Test helper function
        const test = (desc, url, payload, headers, expectedStatus = 400) => {
            const res = http.post(url, payload, { headers });
            const ok = check(res, {
                [`${desc} → ${expectedStatus}+`]: (r) => r.status >= expectedStatus,
            });
            if (!ok) {
                console.error(`${desc} failed →`, res.status, res.body);
            }
        };

        // Headers for different scenarios
        const badHeaders = {
            'Content-Type': 'application/json',
            Authorization: 'Bearer invalid-token',
        };

        const noAuthHeaders = {
            'Content-Type': 'application/json',
        };

        const validHeaders = users && users.length > 0 ? {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${users[0].token}`,
        } : badHeaders;

        // Payloads for testing
        const validInstrumentsPayload = JSON.stringify({
            exchange: EXCHANGE,
            searchPattern: '',
            page: 1,
            pageSize: 10
        });

        const missingInstrumentIdPayload = JSON.stringify({
            exchange: EXCHANGE,
            quantity: 1,
            OrderType: 'MARKET',
            buy: true,
        });

        const invalidInstrumentPayload = JSON.stringify({
            instrumentId: 999999999999,
            exchange: EXCHANGE,
            quantity: 1,
            OrderType: 'MARKET',
            buy: true,
        });

        const invalidQuantityPayload = JSON.stringify({
            instrumentId: 1,
            exchange: EXCHANGE,
            quantity: -1,
            OrderType: 'MARKET',
            buy: true,
        });

        const malformedPayload = '{"exchange": "' + EXCHANGE + '", "quantity": invalid json';

        // === AUTHENTICATION TESTS ===

        test(
            'buyBadMarketScenario POST getInstruments → invalid token',
            `${BASE_URL}/api/user/getInstruments`,
            validInstrumentsPayload,
            badHeaders,
            401
        );

        test(
            'buyBadMarketScenario POST getInstruments → no auth',
            `${BASE_URL}/api/user/getInstruments`,
            validInstrumentsPayload,
            noAuthHeaders,
            401
        );

        test(
            'buyBadMarketScenario POST makeOrder → invalid token',
            `${BASE_URL}/api/user/makeOrder`,
            missingInstrumentIdPayload,
            badHeaders,
            401
        );

        test(
            'buyBadMarketScenario POST makeOrder → no auth',
            `${BASE_URL}/api/user/makeOrder`,
            missingInstrumentIdPayload,
            noAuthHeaders,
            401
        );

        // === VALIDATION TESTS ===

        test(
            'buyBadMarketScenario POST makeOrder → missing instrumentId',
            `${BASE_URL}/api/user/makeOrder`,
            missingInstrumentIdPayload,
            validHeaders
        );

        test(
            'buyBadMarketScenario POST makeOrder → invalid instrumentId',
            `${BASE_URL}/api/user/makeOrder`,
            invalidInstrumentPayload,
            validHeaders
        );

        test(
            'buyBadMarketScenario POST makeOrder → invalid quantity',
            `${BASE_URL}/api/user/makeOrder`,
            invalidQuantityPayload,
            validHeaders
        );

        test(
            'buyBadMarketScenario POST makeOrder → malformed JSON',
            `${BASE_URL}/api/user/makeOrder`,
            malformedPayload,
            validHeaders
        );

        test(
            'buyBadMarketScenario POST getInstruments → malformed JSON',
            `${BASE_URL}/api/user/getInstruments`,
            malformedPayload,
            validHeaders
        );

        // === PORTFOLIO TESTS ===

        test(
            'buyBadMarketScenario POST getPortfolio → invalid token',
            `${BASE_URL}/api/user/getPortfolio`,
            JSON.stringify({ exchange: EXCHANGE }),
            badHeaders,
            401
        );

        test(
            'buyBadMarketScenario POST getPortfolio → no auth',
            `${BASE_URL}/api/user/getPortfolio`,
            JSON.stringify({ exchange: EXCHANGE }),
            noAuthHeaders,
            401
        );

        test(
            'buyBadMarketScenario POST getPortfolio → malformed JSON',
            `${BASE_URL}/api/user/getPortfolio`,
            malformedPayload,
            validHeaders
        );

        sleep(1);
    });
}
