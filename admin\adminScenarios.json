{"GoodScenarios": ["userMappingGoodFlow", "goodWalletEnableAndAddingBalanceScenario", "goodUsers", "goodAdmin<PERSON>ogin", "goodUserTransactions", "goodUserTrades", "goodTransactionsStatusCount", "positiveExchangeEnableFlow", "goodUserCounts", "goodPositionsSummary", "goodUserLimitsAndLeverage", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goodCreateSubagentWithConfigCopy", "goodCreateAgentAndCopyConfig", "loginFromCsv", "createUser", "goodBanScript", "goodManageHolidays"], "BadScenarios": ["badWalletEnableAndAddingBalanceScenario", "badUsers", "badAdminLogin", "badUserTransactions", "badUserTrades", "badTransactionsStatusCount", "negativeExchangeEnableCases", "badUserCounts", "badPositionsSummary", "badUserLimitsAndLeverage", "badStop<PERSON><PERSON><PERSON>oggle", "badCreateSubagentWithConfigCopy", "badCreateAgentAndCopyConfig", "userMappingNegativeFlow", "badBanScript", "badManageHolidays"]}