import http from 'k6/http';
import { check, group, sleep } from 'k6';
import { ADMIN_BASE, ADMIN_EMAIL, ADMIN_PASSWORD } from '../config/config.js';

export let options = { vus: 1, duration: '1s' };

const INVALID_EMAIL = "<EMAIL>";
const INVALID_PASSWORD = "12345";

export default function () {
  group('Admin Login API Tests', () => {
    goodAdminLogin();
    badAdminLogin();
  });
}

// Legacy function names for backward compatibility
export const testGoodAdminLogin = goodAdminLogin;
export const testBadAdminLogin = badAdminLogin;

export function goodAdminLogin() {
  group('✅ Admin Login - Good Flow', () => {
    const payload = JSON.stringify({
      email: ADMIN_EMAIL,
      password: ADMIN_PASSWORD,
    });

    const headers = { 'Content-Type': 'application/json' };

    const res = http.post(`${ADMIN_BASE}/login`, payload, { headers });

    const ok = check(res, {
      'adminLogin POST /login → 200': (r) => r.status === 200,
      'adminLogin POST /login → success status': (r) => {
        try {
          const json = r.json();
          return json.status === 'success';
        } catch (e) {
          console.error('adminLogin POST /login → JSON parse error:', e.message);
          return false;
        }
      },
      'adminLogin POST /login → has token': (r) => {
        try {
          const json = r.json();
          return json.token?.token !== undefined;
        } catch (e) {
          console.error('adminLogin POST /login → token check error:', e.message);
          return false;
        }
      },
      'adminLogin POST /login → valid response structure': (r) => {
        try {
          const json = r.json();
          return json.status !== undefined && json.token !== undefined;
        } catch (e) {
          console.error('adminLogin POST /login → response structure error:', e.message);
          return false;
        }
      },
    });

    if (!ok) {
      console.error('adminLogin POST /login failed:', res.status, res.body);
    }

    sleep(1);
  });
}

export function badAdminLogin() {
  group('❌ Admin Login - Bad Scenarios', () => {

    // Test helper function
    const test = (desc, payload, testHeaders, expectedStatus = 400) => {
      const res = http.post(`${ADMIN_BASE}/login`, payload, { headers: testHeaders });
      const ok = check(res, {
        [`${desc} → ${expectedStatus}+`]: (r) => r.status >= expectedStatus,
      });
      if (!ok) {
        console.error(`${desc} failed →`, res.status, res.body);
      }
    };

    // Test helper function for permissive API behavior (returns 200 with error message)
    const testPermissive = (desc, payload, testHeaders, expectedStatus = 400) => {
      const res = http.post(`${ADMIN_BASE}/login`, payload, { headers: testHeaders });
      const ok = check(res, {
        [`${desc} → ${expectedStatus}+ or 200 (API permissive)`]: (r) => {
          if (r.status >= expectedStatus) {
            return true; // Expected error status
          } else if (r.status === 200) {
            // API is permissive - check for error message in response
            try {
              const json = r.json();
              return json.status === 'fail' && json.message === 'Invalid credentials';
            } catch (e) {
              return false;
            }
          }
          return false;
        },
      });
      if (!ok) {
        console.error(`${desc} failed →`, res.status, res.body);
      }
    };

    const headers = { 'Content-Type': 'application/json' };

    // Test wrong password
    testPermissive(
      'adminLogin POST /login → wrong password',
      JSON.stringify({
        email: ADMIN_EMAIL,
        password: INVALID_PASSWORD,
      }),
      headers
    );

    // Test invalid email
    testPermissive(
      'adminLogin POST /login → invalid email',
      JSON.stringify({
        email: INVALID_EMAIL,
        password: ADMIN_PASSWORD,
      }),
      headers
    );

    // Test missing fields
    test(
      'adminLogin POST /login → missing fields',
      JSON.stringify({}),
      headers
    );

    // Test malformed JSON
    test(
      'adminLogin POST /login → malformed JSON',
      '{ bad json',
      headers
    );

    // Test no content-type header
    const res = http.post(`${ADMIN_BASE}/login`, JSON.stringify({
      email: ADMIN_EMAIL,
      password: ADMIN_PASSWORD,
    }));

    const ok = check(res, {
      'adminLogin POST /login → no content-type → 200+': (r) => r.status >= 200,
    });

    if (!ok) {
      console.error('adminLogin POST /login → no content-type failed:', res.status, res.body);
    }

    sleep(1);
  });
}
