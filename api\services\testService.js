const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');

// Function to trigger K6 test execution
const runK6Test = (req, res) => {
  const { scenario, config } = req.body; // Dynamic inputs from the UI

  // Set the path for K6 test script and results file
  const k6ScriptPath = path.join(__dirname, '../../k6/scenarios', `${scenario}.js`);
  const resultFilePath = path.join(__dirname, '../../logs/results.json');

  // Build the K6 run command
  const command = `k6 run ${k6ScriptPath} --out json=${resultFilePath}`;

  // Execute the K6 command
  exec(command, (error, stdout, stderr) => {
    if (error) {
      console.error(`exec error: ${error}`);
      return res.status(500).json({ error: 'Failed to execute K6 script' });
    }

    if (stderr) {
      console.error(`stderr: ${stderr}`);
      return res.status(500).json({ error: 'Error occurred during K6 execution' });
    }

    // Read the results from the JSON file and return them
    fs.readFile(resultFilePath, 'utf8', (err, data) => {
      if (err) {
        console.error('Error reading result file:', err);
        return res.status(500).json({ error: 'Error reading results' });
      }

      res.status(200).json(JSON.parse(data)); // Send back the results
    });
  });
};

module.exports = { runK6Test };
