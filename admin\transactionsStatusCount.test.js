import http from 'k6/http';
import { check, group } from 'k6';
import { ADMIN_BASE, ADMIN_AUTH_TOKEN, OVERSEE_USER_ID } from '../config/config.js';

const headers = {
  'Authorization': ADMIN_AUTH_TOKEN,
  'Content-Type': 'application/json',
};

export const options = {
  vus: 1,
  iterations: 1,
};

export default function () {
  goodTransactionsStatusCount();
  badTransactionsStatusCount();
}

export function goodTransactionsStatusCount() {
  group('✅ Transactions Status Count - Good Flow', () => {
    const res = http.get(`${ADMIN_BASE}/transactions/status-count/${OVERSEE_USER_ID}`, { headers });

    const ok = check(res, {
      'transactionsStatusCount GET /transactions/status-count → 200': (r) => r.status === 200,
      'transactionsStatusCount GET /transactions/status-count → has Approved key': (r) => {
        try {
          const json = r.json();
          return json.hasOwnProperty('Approved');
        } catch (e) {
          console.error('transactionsStatusCount GET /transactions/status-count → JSON parse error:', e.message);
          return false;
        }
      },
    });

    if (!ok) {
      console.error('transactionsStatusCount GET /transactions/status-count failed:', res.status, res.body);
    }
  });
}

export function badTransactionsStatusCount() {
  group('❌ Transactions Status Count - Bad Scenarios', () => {
    const invalidUserId = 'FAKE-ID';

    const test = (desc, url, hdrs, expectedStatus = 400) => {
      const res = http.get(url, { headers: hdrs });
      const ok = check(res, { [`${desc} → ${expectedStatus}+`]: (r) => r.status >= expectedStatus });
      if (!ok) {
        console.error(`${desc} failed →`, res.status, res.body);
      }
    };

    // Test helper function for permissive API behavior
    const testPermissive = (desc, url, hdrs, expectedStatus = 400) => {
      const res = http.get(url, { headers: hdrs });
      const ok = check(res, {
        [`${desc} → ${expectedStatus}+ or 200 (API permissive)`]: (r) => {
          if (r.status >= expectedStatus) {
            return true; // Expected error status
          } else if (r.status === 200) {
            // API is permissive - check for empty/default response
            try {
              const json = r.json();
              return json.Approved === 0 && json.Rejected === 0 && json.Pending === 0;
            } catch (e) {
              return false;
            }
          }
          return false;
        },
      });
      if (!ok) {
        console.error(`${desc} failed →`, res.status, res.body);
      }
    };

    testPermissive('transactionsStatusCount GET /transactions/status-count → invalid User ID', `${ADMIN_BASE}/transactions/status-count/${invalidUserId}`, headers);
    test('transactionsStatusCount GET /transactions/status-count → missing token', `${ADMIN_BASE}/transactions/status-count/${OVERSEE_USER_ID}`, { 'Content-Type': 'application/json' }, 403);
    test('transactionsStatusCount GET /transactions/status-count → bad token', `${ADMIN_BASE}/transactions/status-count/${OVERSEE_USER_ID}`, {
      ...headers,
      Authorization: 'Bearer invalid',
    }, 401);
  });
}

// Legacy function names for backward compatibility
export const goodTransactionsStatusCountCase = goodTransactionsStatusCount;
export const badTransactionsStatusCountCases = badTransactionsStatusCount;
