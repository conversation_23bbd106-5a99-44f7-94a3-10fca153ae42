import http from 'k6/http';
import { group, check } from 'k6';
import { ADMIN_BASE, ADMIN_AUTH_TOKEN } from '../config/config.js';

const validHeaders = {
    'Content-Type': 'application/json',
    Authorization: ADMIN_AUTH_TOKEN,
};

const badHeaders = {
    'Content-Type': 'application/json',
    Authorization: 'Bearer invalid-token',
};

const noAuthHeaders = {
    'Content-Type': 'application/json',
};

export const options = {
    scenarios: {
        good_users: {
            executor: 'per-vu-iterations',
            vus: 1,
            iterations: 1,
            exec: 'goodUsers',
        },
        bad_users: {
            executor: 'per-vu-iterations',
            vus: 1,
            iterations: 1,
            exec: 'badUsers',
        },
    },
};

export function goodUsers() {
    group('✅ Admin Users - Good Flow', () => {
        const res = http.get(`${ADMIN_BASE}/users`, { headers: validHeaders });

        const ok = check(res, {
            'adminUsers GET /users → 200': (r) => r.status === 200,
            'adminUsers GET /users → returns user list': (r) => {
                try {
                    const json = r.json();
                    return Array.isArray(json) && json.length > 0;
                } catch (e) {
                    console.error('adminUsers GET /users → JSON parse error:', e.message);
                    return false;
                }
            },
        });

        if (!ok) {
            console.error('adminUsers GET /users failed:', res.status, res.body);
        }

        // Test /users/by endpoint
        const res2 = http.get(`${ADMIN_BASE}/users/by?user_role=IU&oversee_id=AG-OKIDA6&page_number=1&page_size=10`, {
            headers: validHeaders,
        });

        const ok2 = check(res2, {
            'adminUsers GET /users/by → 200': (r) => r.status === 200,
            'adminUsers GET /users/by → has count and rows': (r) => {
                try {
                    const json = r.json();
                    return typeof json?.count === 'number' && Array.isArray(json?.rows);
                } catch (e) {
                    console.error('adminUsers GET /users/by → JSON parse error:', e.message);
                    return false;
                }
            },
        });

        if (!ok2) {
            console.error('adminUsers GET /users/by failed:', res2.status, res2.body);
        }
    });
}

export function badUsers() {
  group('❌ Admin Users - Bad Scenarios', () => {
    const test = (desc, url, headersObj, expectStatus = 400) => {
      const res = http.get(url, { headers: headersObj });
      const ok = check(res, {
        [`${desc} → ${expectStatus}+`]: (r) => r.status >= expectStatus,
      });
      if (!ok) {
        console.error(`${desc} failed →`, res.status, res.body);
      }
    };

    // --- /users endpoint ---
    test('adminUsers GET /users → missing token', `${ADMIN_BASE}/users`, noAuthHeaders, 403);
    test('adminUsers GET /users → invalid token', `${ADMIN_BASE}/users`, badHeaders, 401);

    // --- /users/by endpoint ---
    test(
      'adminUsers GET /users/by → bad token',
      `${ADMIN_BASE}/users/by?user_role=IU&oversee_id=AG-OKIDA6&page_number=1&page_size=10`,
      badHeaders,
      401
    );
    test(
      'adminUsers GET /users/by → no token',
      `${ADMIN_BASE}/users/by?user_role=IU&oversee_id=AG-OKIDA6&page_number=1&page_size=10`,
      noAuthHeaders,
      403
    );

    // Fixed logic: missing params still returns 200 OK with empty result
    group('⚠️ adminUsers GET /users/by → missing params (expect 200, empty)', () => {
      const res = http.get(`${ADMIN_BASE}/users/by`, { headers: validHeaders });

      const ok = check(res, {
        'adminUsers GET /users/by (missing params) → 200': (r) => r.status === 200,
        'adminUsers GET /users/by (missing params) → empty result': (r) => {
          try {
            const json = r.json();
            return json?.count === 0 && Array.isArray(json?.rows) && json.rows.length === 0;
          } catch (e) {
            console.error('adminUsers GET /users/by (missing params) → JSON parse error:', e.message);
            return false;
          }
        },
      });

      if (!ok) {
        console.error('adminUsers GET /users/by → missing params failed:', res.status, res.body);
      }
    });

    test(
      'adminUsers GET /users/by → malformed param values',
      `${ADMIN_BASE}/users/by?user_role=!!&oversee_id=@@@&page_number=-5&page_size=999999`,
      validHeaders,
      400
    );
  });
}
