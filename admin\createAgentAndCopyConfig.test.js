import http from 'k6/http';
import { check, group } from 'k6';
import {
  ADMIN_BASE,
  ADMIN_AUTH_TOKEN,OVERSEE_USER_ID
} from '../config/config.js';

const validHeaders = {
  'Content-Type': 'application/json',
  Authorization: ADMIN_AUTH_TOKEN,
};

const noAuthHeaders = {
  'Content-Type': 'application/json',
};

const badHeaders = {
  'Content-Type': 'application/json',
  Authorization: 'Bearer invalid-token',
};

const OLD_USER_ID = OVERSEE_USER_ID; // template user to copy from

export const options = {
  vus: 1,
  iterations: 1,
};

export default function () {
  goodCreateAgentAndCopyConfig();
  badCreateAgentAndCopyConfig();
}

export function goodCreateAgentAndCopyConfig() {
  group('✅ Create Agent And Copy Config - Good Flow', () => {
    const email = `agent_${Date.now()}@test.com`;

    // Step 1: Create agent
    const createPayload = JSON.stringify({
      first_name: 'Test',
      last_name: 'Agent',
      email,
      user_role: 'AG',
      oversee_user: 'AU-MAIN',
      agents_limit: 10,
      users_limit: 100,
      is_shared: true,
      shared_percentage: 0.01,
      is_brokerage_shared: true,
      brokerage_sharing: 0.01,
      is_additional_brokerage_shared: true,
      additional_brokerage_sharing: 0.01
    });

    const createRes = http.post(`${ADMIN_BASE}/user`, createPayload, { headers: validHeaders });

    const createOk = check(createRes, {
      'createAgentAndCopyConfig POST /user → 200': (r) => r.status === 200,
      'createAgentAndCopyConfig POST /user → has user_id': (r) => {
        try {
          const json = r.json();
          return !!json.user_id;
        } catch (e) {
          console.error('createAgentAndCopyConfig POST /user → JSON parse error:', e.message);
          return false;
        }
      },
      'createAgentAndCopyConfig POST /user → has password': (r) => {
        try {
          const json = r.json();
          return !!json.password;
        } catch (e) {
          console.error('createAgentAndCopyConfig POST /user → password check error:', e.message);
          return false;
        }
      }
    });

    if (!createOk) {
      console.error("createAgentAndCopyConfig POST /user failed:", createRes.status, createRes.body);
      return;
    }

    let newUserId;
    try {
      newUserId = createRes.json().user_id;
    } catch (e) {
      console.error('createAgentAndCopyConfig POST /user → failed to extract user_id:', e.message);
      return;
    }

    // Step 2: Copy config
    const copyPayload = JSON.stringify({
      new_user_id: newUserId,
      old_user_id: OLD_USER_ID
    });

    const copyRes = http.post(`${ADMIN_BASE}/copyAgentConfig`, copyPayload, { headers: validHeaders });

    const copyOk = check(copyRes, {
      'createAgentAndCopyConfig POST /copyAgentConfig → 200': (r) => r.status === 200,
      'createAgentAndCopyConfig POST /copyAgentConfig → success message': (r) => {
        try {
          const responseText = r.body;
          return responseText.includes("Copied Successfully");
        } catch (e) {
          console.error('createAgentAndCopyConfig POST /copyAgentConfig → response check error:', e.message);
          return false;
        }
      }
    });

    if (!copyOk) {
      console.error("createAgentAndCopyConfig POST /copyAgentConfig failed:", copyRes.status, copyRes.body);
    } else {
      console.log('✅ Agent created and config copied successfully');
    }
  });
}

export function badCreateAgentAndCopyConfig() {
  group('❌ Create Agent And Copy Config - Bad Scenarios', () => {

    // Test helper function
    const test = (desc, url, payload, testHeaders, expectedStatus = 400) => {
      const res = http.post(url, payload, { headers: testHeaders });
      const ok = check(res, {
        [`${desc} → ${expectedStatus}+`]: (r) => r.status >= expectedStatus,
      });
      if (!ok) {
        console.error(`${desc} failed →`, res.status, res.body);
      }
    };

    const createUrl = `${ADMIN_BASE}/user`;
    const copyUrl = `${ADMIN_BASE}/users/copy-config`;

    // 1. Create with missing fields
    test(
      'createAgentAndCopyConfig POST /user → missing fields',
      createUrl,
      JSON.stringify({}),
      validHeaders
    );

    // 2. Create with invalid email
    test(
      'createAgentAndCopyConfig POST /user → invalid email',
      createUrl,
      JSON.stringify({
        first_name: "Bad",
        last_name: "Email",
        email: "bademail",
        user_role: "AG",
        oversee_user: "AU-MAIN"
      }),
      validHeaders
    );

    // 3. Duplicate email
    test(
      'createAgentAndCopyConfig POST /user → duplicate email',
      createUrl,
      JSON.stringify({
        first_name: "Dup",
        last_name: "User",
        email: "<EMAIL>", // from your real example
        user_role: "AG",
        oversee_user: "AU-MAIN"
      }),
      validHeaders
    );

    // 4. Create with malformed JSON
    test(
      'createAgentAndCopyConfig POST /user → malformed JSON',
      createUrl,
      '{"first_name": "Test", "email": invalid json',
      validHeaders
    );

    // 5. Create with no auth
    test(
      'createAgentAndCopyConfig POST /user → no auth',
      createUrl,
      JSON.stringify({
        first_name: "Test",
        last_name: "Agent",
        email: "<EMAIL>",
        user_role: "AG",
        oversee_user: "AU-MAIN"
      }),
      noAuthHeaders,
      403
    );

    // 6. Create with bad token
    test(
      'createAgentAndCopyConfig POST /user → bad token',
      createUrl,
      JSON.stringify({
        first_name: "Test",
        last_name: "Agent",
        email: "<EMAIL>",
        user_role: "AG",
        oversee_user: "AU-MAIN"
      }),
      badHeaders,
      401
    );

    // 7. Copy config with missing payload
    test(
      'createAgentAndCopyConfig POST /users/copy-config → empty body',
      copyUrl,
      JSON.stringify({}),
      validHeaders
    );

    // 8. Copy config with invalid IDs
    test(
      'createAgentAndCopyConfig POST /users/copy-config → invalid IDs',
      copyUrl,
      JSON.stringify({
        new_user_id: "FAKE-USER",
        old_user_id: "INVALID"
      }),
      validHeaders
    );

    // 9. Copy config with malformed JSON
    test(
      'createAgentAndCopyConfig POST /users/copy-config → malformed JSON',
      copyUrl,
      '{"new_user_id": "AG-XXXX", "old_user_id": invalid json',
      validHeaders
    );

    // 10. Copy config without token
    test(
      'createAgentAndCopyConfig POST /users/copy-config → no auth',
      copyUrl,
      JSON.stringify({
        new_user_id: "AG-XXXX",
        old_user_id: OLD_USER_ID
      }),
      noAuthHeaders,
      403
    );

    // 11. Copy config with bad token
    test(
      'createAgentAndCopyConfig POST /users/copy-config → bad token',
      copyUrl,
      JSON.stringify({
        new_user_id: "AG-XXXX",
        old_user_id: OLD_USER_ID
      }),
      badHeaders,
      401
    );
  });
}

// Legacy function names for backward compatibility
export const goodAgentCreationAndConfigCopy = goodCreateAgentAndCopyConfig;
export const badAgentCreationAndCopyCases = badCreateAgentAndCopyConfig;
