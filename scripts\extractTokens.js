const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');

// Configuration
const CONFIG = {
  LOG_FILE: path.join(__dirname, '../login_output.log'),
  CSV_OUTPUT_PATH: path.join(__dirname, '../data/users_with_tokens.csv'),
  TOKENS_OUTPUT_PATH: path.join(__dirname, '../data/tokens.json'),
  USERS_OUTPUT_PATH: path.join(__dirname, '../data/users_summary.json'),
  POWERSHELL_SCRIPT: path.join(__dirname, 'extract-tokens.ps1'),
};

/**
 * Run PowerShell script to extract tokens from log file
 */
function runPowerShellExtraction() {
  return new Promise((resolve, reject) => {
    console.log('🔍 Running PowerShell token extraction...');

    // Check if log file exists
    if (!fs.existsSync(CONFIG.LOG_FILE)) {
      reject(new Error(`Log file not found: ${CONFIG.LOG_FILE}. Run K6 login script first.`));
      return;
    }

    // Check if PowerShell script exists
    if (!fs.existsSync(CONFIG.POWERSHELL_SCRIPT)) {
      reject(new Error(`PowerShell script not found: ${CONFIG.POWERSHELL_SCRIPT}`));
      return;
    }

    const psCommand = `powershell -ExecutionPolicy Bypass -File "${CONFIG.POWERSHELL_SCRIPT}" -logPath "${CONFIG.LOG_FILE}" -outputCsv "${CONFIG.CSV_OUTPUT_PATH}"`;

    console.log(`📝 Executing: ${psCommand}`);

    exec(psCommand, (error, stdout, stderr) => {
      if (error) {
        console.error('❌ PowerShell execution failed:', error.message);
        reject(error);
        return;
      }

      if (stderr) {
        console.warn('⚠️ PowerShell warnings:', stderr);
      }

      console.log('✅ PowerShell extraction completed');
      console.log(stdout);
      resolve(stdout);
    });
  });
}

/**
 * Read users with tokens from CSV (after PowerShell extraction)
 */
function readUsersWithTokens() {
  try {
    if (!fs.existsSync(CONFIG.CSV_OUTPUT_PATH)) {
      console.error(`❌ CSV file not found: ${CONFIG.CSV_OUTPUT_PATH}`);
      console.log('💡 Run PowerShell extraction first');
      return [];
    }

    const csvContent = fs.readFileSync(CONFIG.CSV_OUTPUT_PATH, 'utf8');
    const lines = csvContent.split('\n').filter(line => line.trim());

    if (lines.length <= 1) {
      console.error('❌ CSV file is empty or only contains header');
      return [];
    }

    // Parse CSV manually (simple approach)
    const users = [];
    const header = lines[0].split(',');

    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(',');
      if (values.length >= 3) {
        users.push({
          user_id: values[0],
          password: values[1],
          token: values[2]
        });
      }
    }

    console.log(`✅ Loaded ${users.length} users from CSV`);
    return users;
  } catch (error) {
    console.error('❌ Error reading CSV file:', error.message);
    return [];
  }
}

/**
 * Extract and categorize tokens
 */
function extractTokens(users) {
  const validTokens = [];
  const invalidUsers = [];
  const summary = {
    total: users.length,
    withValidTokens: 0,
    withInvalidTokens: 0,
    loginFailed: 0,
    noToken: 0
  };

  users.forEach(user => {
    // Simple check: if token exists and is not empty
    if (user.token && user.token.trim() && user.token !== '') {
      validTokens.push({
        user_id: user.user_id,
        token: user.token.trim(),
        login_time: new Date().toISOString()
      });
      summary.withValidTokens++;
    } else {
      invalidUsers.push({
        user_id: user.user_id,
        status: 'no_token',
        error: 'Token is empty or missing'
      });
      summary.noToken++;
    }
  });

  return { validTokens, invalidUsers, summary };
}

/**
 * Save tokens to JSON file
 */
function saveTokensToJson(validTokens) {
  try {
    const tokensData = {
      generated_at: new Date().toISOString(),
      count: validTokens.length,
      tokens: validTokens
    };
    
    fs.writeFileSync(CONFIG.TOKENS_OUTPUT_PATH, JSON.stringify(tokensData, null, 2));
    console.log(`✅ Valid tokens saved to: ${CONFIG.TOKENS_OUTPUT_PATH}`);
    return tokensData;
  } catch (error) {
    console.error('❌ Error saving tokens JSON:', error.message);
    throw error;
  }
}

/**
 * Save summary to JSON file
 */
function saveSummaryToJson(summary, invalidUsers) {
  try {
    const summaryData = {
      generated_at: new Date().toISOString(),
      summary,
      invalid_users: invalidUsers
    };
    
    fs.writeFileSync(CONFIG.USERS_OUTPUT_PATH, JSON.stringify(summaryData, null, 2));
    console.log(`✅ Users summary saved to: ${CONFIG.USERS_OUTPUT_PATH}`);
    return summaryData;
  } catch (error) {
    console.error('❌ Error saving summary JSON:', error.message);
    throw error;
  }
}

/**
 * Display summary
 */
function displaySummary(summary) {
  console.log('\n📊 TOKEN EXTRACTION SUMMARY');
  console.log('═'.repeat(40));
  console.log(`Total Users: ${summary.total}`);
  console.log(`✅ Valid Tokens: ${summary.withValidTokens}`);
  console.log(`❌ Login Failed: ${summary.loginFailed}`);
  console.log(`⚠️ No Token: ${summary.noToken}`);
  console.log(`❓ Invalid/Unknown: ${summary.withInvalidTokens}`);
  console.log('═'.repeat(40));
  
  const successRate = summary.total > 0 ? ((summary.withValidTokens / summary.total) * 100).toFixed(1) : 0;
  console.log(`Success Rate: ${successRate}%`);
}

/**
 * Main function
 */
async function main() {
  console.log('🔍 Starting token extraction process...\n');

  try {
    // Step 1: Run PowerShell script to extract tokens from log file
    console.log('📋 Step 1: Running PowerShell extraction...');
    await runPowerShellExtraction();

    // Step 2: Read the generated CSV file
    console.log('📋 Step 2: Reading extracted CSV data...');
    const users = readUsersWithTokens();
    if (users.length === 0) {
      console.error('❌ No users found in extracted CSV file');
      process.exit(1);
    }

    // Step 3: Extract and categorize tokens
    console.log('📋 Step 3: Processing token data...');
    const { validTokens, invalidUsers, summary } = extractTokens(users);

    // Step 4: Save results to JSON files
    console.log('📋 Step 4: Saving results...');
    const tokensData = saveTokensToJson(validTokens);
    const summaryData = saveSummaryToJson(summary, invalidUsers);

    // Step 5: Display summary
    displaySummary(summary);

    console.log('\n🎉 Token extraction completed successfully!');

    return {
      tokensData,
      summaryData,
      summary
    };

  } catch (error) {
    console.error('❌ Fatal error:', error.message);
    process.exit(1);
  }
}

// Export for use as module
module.exports = {
  main,
  extractTokens,
  readUsersWithTokens,
  saveTokensToJson,
  saveSummaryToJson,
  CONFIG
};

// Run if called directly
if (require.main === module) {
  main();
}
