# Simple K6 Login Flow with Token Extraction

This is a simple, proven workflow for logging in users from CSV and extracting tokens using K6 and PowerShell.

## 🔄 Simple Workflow

### **Step 1: Prepare Input CSV**
Create `data/users.csv` with your user credentials:
```csv
user_id,password
IU-USER001,password123
IU-USER002,password456
IU-USER003,password789
```

### **Step 2: Run K6 Login Script**
```bash
# Method 1: Direct command (your original way)
k6 run scripts/loginWithCsv.js > login_output.log 2>&1

# Method 2: Via API
curl -X POST http://localhost:4000/api/users/login-csv
```

### **Step 3: Extract Tokens with PowerShell**
```bash
# Method 1: Direct PowerShell (your original way)
.\scripts\extract-tokens.ps1

# Method 2: Via API (runs PowerShell automatically)
curl -X POST http://localhost:4000/api/users/extract-tokens
```

### **Step 4: Use Generated CSV**
The process creates `data/users_with_tokens.csv`:
```csv
user_id,password,token
IU-USER001,password123,eyJhbGciOiJIUzI1NiIs...
IU-USER002,password456,eyJhbGciOiJIUzI1NiIs...
IU-USER003,password789,
```

## 📁 File Structure

```
project/
├── scripts/
│   ├── loginWithCsv.js      # K6 script for login
│   ├── extract-tokens.ps1   # PowerShell extraction script
│   └── extractTokens.js     # Node.js wrapper (calls PowerShell)
├── data/
│   ├── users.csv            # Input: user credentials
│   └── users_with_tokens.csv # Output: users with tokens
├── login_output.log         # K6 output log
└── server.js               # API endpoints
```

## 🚀 API Endpoints

### **POST /api/users/login-csv**
Runs K6 login script and generates log file.

**Response:**
```json
{
  "success": true,
  "message": "K6 CSV login process completed",
  "summary": {
    "totalAttempts": 10,
    "successful": 8,
    "failed": 2
  },
  "logFile": "/path/to/login_output.log",
  "nextStep": "Run extract-tokens to generate CSV file"
}
```

### **POST /api/users/extract-tokens**
Runs PowerShell script to extract tokens from log file.

**Response:**
```json
{
  "success": true,
  "message": "PowerShell token extraction completed",
  "csvSummary": {
    "total": 8,
    "withTokens": 6
  },
  "files": {
    "csv": true,
    "tokens": true,
    "summary": true
  }
}
```

### **GET /api/users/csv-status**
Check status of all files.

**Response:**
```json
{
  "success": true,
  "status": {
    "csv": {
      "exists": true,
      "userCount": 8,
      "successfulLogins": 6,
      "failedLogins": 2
    },
    "tokens": {
      "exists": true,
      "size": 1024
    }
  }
}
```

## 🔧 Manual Commands (Your Original Way)

```bash
# 1. Run K6 login
k6 run scripts/loginWithCsv.js > login_output.log 2>&1

# 2. Extract tokens with PowerShell
powershell -ExecutionPolicy Bypass -File scripts/extract-tokens.ps1

# 3. Check results
cat data/users_with_tokens.csv
```

## 🎯 Test Scenarios Integration

Your test scenarios automatically detect the test mode:

**Load Testing Mode (uses CSV):**
```bash
# Set environment variable
$env:TEST_MODE = "load"
k6 run scenarios/buyMarketOrder.test.js
```

**Scenario Testing Mode (uses static config):**
```bash
# Set environment variable  
$env:TEST_MODE = "scenario"
k6 run scenarios/buyMarketOrder.test.js
```

## 📊 Example Complete Workflow

```bash
# 1. Start server
node server.js

# 2. Check if users.csv exists
curl http://localhost:4000/api/users/csv-status

# 3. Run login process
curl -X POST http://localhost:4000/api/users/login-csv

# 4. Extract tokens
curl -X POST http://localhost:4000/api/users/extract-tokens

# 5. Check final status
curl http://localhost:4000/api/users/csv-status

# 6. Run load test with CSV data
curl -X POST http://localhost:4000/api/run-k6 \
  -H "Content-Type: application/json" \
  -d '{
    "scenarios": ["buyGoodMarketScenario"],
    "testMode": "load"
  }'
```

## 🛠️ Troubleshooting

### **"No users found in CSV"**
- Check if `data/users.csv` exists
- Verify CSV format (header: `user_id,password`)

### **"Login log file not found"**
- Run the login step first: `k6 run scripts/loginWithCsv.js > login_output.log 2>&1`

### **"PowerShell execution failed"**
- Check PowerShell execution policy: `Get-ExecutionPolicy`
- Set if needed: `Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser`

### **"No tokens extracted"**
- Check if login was successful in `login_output.log`
- Verify API endpoints are working
- Check if users have correct credentials

## ✅ Success Indicators

1. **After login**: `login_output.log` contains "Token fetched for" messages
2. **After extraction**: `data/users_with_tokens.csv` exists with token data
3. **In tests**: Console shows "✅ Using CSV user: IU-XXXXX (VU: 1, Mode: load)"

## 🎉 Benefits

- ✅ **Simple and proven** - Uses your existing working scripts
- ✅ **No complex dependencies** - Just K6, PowerShell, and Node.js
- ✅ **API integration** - Can be automated via REST calls
- ✅ **Flexible** - Works with both manual commands and API
- ✅ **Reliable** - Based on your tested workflow
