import { sleep, check, group } from 'k6';
import http from 'k6/http';
import { BASE_URL, ADMIN_BASE, EXCHANGE } from '../config/config.js';
import { getUserSafe, safeJson } from '../utils/csvUserManager.js';

export const options = {
  scenarios: {
    limit_order_good: {
     executor: 'per-vu-iterations',
      vus: 1,
      iterations: 1,
      exec: 'sellGoodLimitOrderFlow',
    },
    limit_order_negative: {
      executor: 'per-vu-iterations',
      vus: 1,
      iterations: 1,
      exec: 'sellBadLimitOrderFlow',
    },
  },
};

export function sellGoodLimitOrderFlow() {
  const { user_id, password, token } = getUserSafe();

  if (!user_id || !token) {
    console.error(`❌ No valid user available for VU ${__VU}`);
    return;
  }

  const headers = {
    'Content-Type': 'application/json',
    Authorization: `Bearer ${token}`,
  };

  group(`✅ SELL Limit Order for ${user_id}`, () => {
    const logPrefix = '[sellLimitOrder]';

    // 1. Wallet BEFORE
    const walletBeforeRes = http.get(`${ADMIN_BASE}/wallets/balance/${user_id}`, { headers });
    const walletBeforeOk = check(walletBeforeRes, { 'sellLimitOrder_wallet before → 200': (r) => r.status === 200 });
    if (!walletBeforeOk) {
      console.error(`sellLimitOrder_wallet Before Response: ${walletBeforeRes.status}, ${walletBeforeRes.body}`);
    }

    const walletBeforeJson = safeJson(walletBeforeRes);
    const netBefore = parseFloat(walletBeforeJson?.wallets?.find(w => w.exchange === EXCHANGE)?.net_balance || 0);
    console.log(`${logPrefix} 💰 Net Balance BEFORE: ${netBefore}`);

    // 2. Get Instrument
    const instPayload = {
      exchange: EXCHANGE,
      searchPattern: '',
      page: 1,
      pageSize: 1,
    };
    const instrumentRes = http.post(`${BASE_URL}/api/user/getInstruments`, JSON.stringify(instPayload), { headers });
    const instOk = check(instrumentRes, { 'sellLimitOrder_getInstruments → 200': (r) => r.status === 200 });
    if (!instOk) {
      console.error(`sellLimitOrder_getInstruments Payload: ${JSON.stringify(instPayload, null, 2)}`);
      console.error(`sellLimitOrder_getInstruments Response: ${instrumentRes.status}, ${instrumentRes.body}`);
      return;
    }

    const instrumentJson = safeJson(instrumentRes);
    const instrument = instrumentJson?.instruments?.[0];
    if (!instrument) {
      console.error(`sellLimitOrder_getInstruments No instrument found`);
      return;
    }
    const {
      FinancialInstrumentID: instrumentId,
      TradingSymbol: symbol,
      LastPrice: lastPriceRaw,
      LowerCkt: lowerCktRaw
    } = instrument;

    const lastPrice = parseFloat(lastPriceRaw);
    const lowerCkt = parseFloat(lowerCktRaw);

    // ❌ Early exit for invalid market data
    if (!lastPrice || lastPrice === 0) {
      console.error(`sellLimitOrder Skipping: Invalid instrument price data → LastPrice: ${lastPrice}`);
      return;
    }

    // Check if lower circuit is valid, if not, use a safe approach
    if (!lowerCkt || lowerCkt === 0) {
      console.error(`sellLimitOrder Skipping: Invalid instrument price data → LastPrice: ${lastPrice}, LowerCkt: ${lowerCkt}`);
      return;
    }

    const triggerPrice = parseFloat((lastPrice * 1.03).toFixed(2));

    // ❌ Skip if trigger is below lower circuit
    if (triggerPrice <= lowerCkt) {
      console.error(`sellLimitOrder triggerPrice too low: ${triggerPrice} <= LowerCkt: ${lowerCkt}`);
      return;
    }


    // 3. Place Order
    const orderPayload = {
      instrumentId,
      exchange: EXCHANGE,
      quantity: 1,
      OrderType: 'LIMIT',
      buy: false,
      triggerPrice,
    };
    const orderRes = http.post(`${BASE_URL}/api/user/makeOrder`, JSON.stringify(orderPayload), { headers });
    const orderOk = check(orderRes, {
      'sellLimitOrder_makeOrder → 200': (r) => r.status === 200,
      'sellLimitOrder_makeOrder → success': (r) => r.json()?.status === 'success',
      'sellLimitOrder_Funds blocked > 0': (r) => parseFloat(r.json()?.message?.match(/Funds blocked: ([\d.]+)/)?.[1] || 0) > 0,
    });
    if (!orderOk) {
      console.error(`sellLimitOrder_makeOrder Payload: ${JSON.stringify(orderPayload, null, 2)}`);
      console.error(`sellLimitOrder_makeOrder Response: ${orderRes.status}, ${orderRes.body}`);
      return;
    }

    console.log(`${logPrefix} ✅ SELL Limit order placed`);

    sleep(1);

    // 4. Wallet AFTER
    const walletAfterRes = http.get(`${ADMIN_BASE}/wallets/balance/${user_id}`, { headers });
    const walletAfterOk = check(walletAfterRes, { 'sellLimitOrder_wallet after → 200': (r) => r.status === 200 });
    if (!walletAfterOk) {
      console.error(`sellLimitOrder_wallet After Response: ${walletAfterRes.status}, ${walletAfterRes.body}`);
    }

    const walletAfterJson = safeJson(walletAfterRes);
    const netAfter = parseFloat(walletAfterJson?.wallets?.find(w => w.exchange === EXCHANGE)?.net_balance || 0);
    console.log(`${logPrefix} 💸 Net Balance AFTER: ${netAfter}`);

    // 5. Check if long position exists
    const portRes = http.post(`${BASE_URL}/api/user/getPortfolio`, JSON.stringify({ exchange: EXCHANGE }), { headers });
    const portOk = check(portRes, { 'sellLimitOrder_getPortfolio → 200': (r) => r.status === 200 });
    if (!portOk) {
      console.error(`sellLimitOrder_getPortfolio Response: ${portRes.status}, ${portRes.body}`);
    }

    const portfolioJson = safeJson(portRes);
    const portfolio = portfolioJson?.portfolios || [];
    const matching = portfolio.find(p => p.FinancialInstrumentID === instrumentId);
    const hasLong = matching && matching.Quantity > 0;
    console.log(`${logPrefix} 📊 Position check for SELL: Qty = ${matching?.Quantity ?? 'N/A'}`);

    check(null, {
      [`${logPrefix} 🧾 net_balance reduced after SELL LIMIT`]: () => netAfter < netBefore,
    });

    // 6. openOrders
    const openRes = http.post(`${BASE_URL}/api/user/openOrders`, JSON.stringify({ exchange: EXCHANGE }), { headers });
    const openOk = check(openRes, { 'openOrders → 200': (r) => r.status === 200 });
    if (!openOk) {
      console.error(`${logPrefix} 📥 openOrders Response: ${openRes.status}, ${openRes.body}`);
    }

    const openJson = safeJson(openRes);
    const order = openJson?.orders?.find(o => o.instrumentId === instrumentId && o.orderType === 'LIMIT');
    if (!order) {
      console.error(`${logPrefix} ❌ Order not found in openOrders`);
      return;
    }

    const orderId = order.ID;
    console.log(`${logPrefix} 📌 Order ID: ${orderId}`);

    // 7. Modify
    const modifyPayload = {
      exchange: EXCHANGE,
      orderId,
      triggerPrice: triggerPrice + 1,
      quantity: 2,
    };
    const modifyRes = http.post(`${BASE_URL}/api/user/modifyOrder`, JSON.stringify(modifyPayload), { headers });
    const modifyOk = check(modifyRes, {
      'sellLimitOrder_modifyOrder → 200': (r) => r.status === 200,
      'sellLimitOrder_modifyOrder → success': (r) => r.json()?.status === 'success',
    });
    if (!modifyOk) {
      console.error(`sellLimitOrder_modifyOrder Payload: ${JSON.stringify(modifyPayload, null, 2)}`);
      console.error(`sellLimitOrder_modifyOrder Response: ${modifyRes.status}, ${modifyRes.body}`);
      return;
    }
    console.log(`${logPrefix} 🔧 Order modified`);

    // 8. Cancel
    const cancelPayload = { exchange: EXCHANGE, orderId };
    const cancelRes = http.post(`${BASE_URL}/api/user/cancelOrder`, JSON.stringify(cancelPayload), { headers });
    const cancelOk = check(cancelRes, {
      'sellLimitOrder_cancelOrder → 200': (r) => r.status === 200,
      'sellLimitOrder_cancelOrder → success': (r) => r.json()?.status === 'success',
    });
    if (!cancelOk) {
      console.error(`sellLimitOrder_cancelOrder Payload: ${JSON.stringify(cancelPayload, null, 2)}`);
      console.error(`sellLimitOrder_cancelOrder Response: ${cancelRes.status}, ${cancelRes.body}`);
      return;
    }

    console.log(`${logPrefix} 🗑️ Order cancelled`);
    sleep(1);
  });
}


export function sellBadLimitOrderFlow() {
  const { user_id, password, token } = getUserSafe();

  if (!user_id || !token) {
    console.error(`❌ No valid user available for VU ${__VU}`);
    return;
  }

  const headers = {
    'Content-Type': 'application/json',
    Authorization: `Bearer ${token}`,
  };

  group('❌ Limit Order - Negative Scenarios', () => {
    const invalidId = 999999999999;

    // 1. Missing triggerPrice
    const missingTrigger = http.post(`${BASE_URL}/api/user/makeOrder`, JSON.stringify({
      instrumentId: invalidId,
      exchange: EXCHANGE,
      quantity: 1,
      OrderType: 'LIMIT',
      buy: false
    }), { headers });

    check(missingTrigger, {
      'sellLimitOrder_makeOrder → 400/422 on missing triggerPrice': (r) => r.status >= 400,
    });

    // 2. Invalid instrument ID
    const invalidInstrument = http.post(`${BASE_URL}/api/user/makeOrder`, JSON.stringify({
      instrumentId: invalidId,
      exchange: EXCHANGE,
      quantity: 1,
      OrderType: 'LIMIT',
      buy: false,
      triggerPrice: 100,
    }), { headers });

    check(invalidInstrument, {
      'sellLimitOrder_makeOrder → fail on invalid instrumentId': (r) => r.status !== 200 || r.json().status !== 'success',
    });

    // 3. Too low triggerPrice (below lower circuit)
    const instrumentRes = http.post(`${BASE_URL}/api/user/getInstruments`, JSON.stringify({
      exchange: EXCHANGE,
      searchPattern: 't',
      page: 1,
      pageSize: 1,
    }), { headers });

    const instrumentJson = safeJson(instrumentRes);
    const instrument = instrumentJson?.instruments?.[0];
    if (instrument) {
      const badPrice = parseFloat((instrument.LowerCkt - 5).toFixed(2));

      const tooLowPrice = http.post(`${BASE_URL}/api/user/makeOrder`, JSON.stringify({
        instrumentId: instrument.FinancialInstrumentID,
        exchange: EXCHANGE,
        quantity: 1,
        OrderType: 'LIMIT',
        buy: false,
        triggerPrice: badPrice,
      }), { headers });

      check(tooLowPrice, {
        'sellLimitOrder_makeOrder → 400 on too low triggerPrice': (r) => r.status >= 400 || r.json().status !== 'success',
      });
    }

    sleep(1);
  });
}
