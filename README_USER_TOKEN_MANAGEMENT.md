# User Token Management & Test Execution System

This system provides comprehensive user token management and intelligent test execution that automatically switches between scenario testing (static auth) and load testing (CSV-based multi-user auth).

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Scripts       │    │   API Layer     │    │   Test Runner   │
│                 │    │                 │    │                 │
│ loginWithCsv.js │◄──►│ userTokenApi.js │◄──►│ k6TestRunner.js │
│ extractTokens.js│    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   CSV Files     │    │   JSON Data     │    │   Test Logs     │
│                 │    │                 │    │                 │
│ users.csv       │    │ tokens.json     │    │ scenario-*.txt  │
│ users_tokens.csv│    │ summary.json    │    │ load-*.txt      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📁 File Structure

```
project/
├── scripts/
│   ├── loginWithCsv.js      # Login all users and generate tokens
│   └── extractTokens.js     # Extract tokens to JSON format
├── api/
│   ├── userTokenApi.js      # User token management endpoints
│   └── k6TestRunner.js      # Enhanced K6 test runner
├── utils/
│   └── csvUserManager.js    # Smart user management (updated)
├── data/
│   ├── users.csv            # Input: user credentials
│   ├── users_with_tokens.csv # Output: users with tokens
│   ├── tokens.json          # Output: extracted tokens
│   └── users_summary.json   # Output: login summary
└── logs/
    ├── scenario-*.txt       # Scenario test logs
    └── load-*.txt          # Load test logs
```

## 🚀 Quick Start

### 1. Setup User Credentials
Create `data/users.csv` with your user credentials:
```csv
user_id,password
IU-USER001,password123
IU-USER002,password456
IU-USER003,password789
```

### 2. Login Users and Generate Tokens
```bash
# Method 1: Direct script execution
cd scripts
node loginWithCsv.js

# Method 2: Via API
curl -X POST http://localhost:3000/api/users/login-csv
```

### 3. Extract Tokens to JSON
```bash
# Method 1: Direct script execution
cd scripts
node extractTokens.js

# Method 2: Via API
curl -X POST http://localhost:3000/api/users/extract-tokens
```

### 4. Run Tests

#### Scenario Testing (Single User, Static Auth)
```bash
curl -X POST http://localhost:3000/api/run-k6 \
  -H "Content-Type: application/json" \
  -d '{
    "scenarios": ["buyGoodMarketScenario", "sellGoodMarketOrder"],
    "testMode": "scenario",
    "staticAuth": {
      "user_id": "IU-USER001",
      "auth_token": "Bearer your-static-token"
    }
  }'
```

#### Load Testing (Multiple Users, CSV Auth)
```bash
curl -X POST http://localhost:3000/api/run-k6 \
  -H "Content-Type: application/json" \
  -d '{
    "scenarios": ["buyGoodMarketScenario", "sellGoodMarketOrder"],
    "testMode": "load",
    "loadConfig": {
      "RAMP_CONFIG": {
        "executor": "ramping-vus",
        "startVUs": 0,
        "stages": [
          {"duration": "30s", "target": 5},
          {"duration": "2m", "target": 5},
          {"duration": "30s", "target": 0}
        ],
        "gracefulStop": "30s"
      }
    }
  }'
```

## 📡 API Endpoints

### User Token Management

#### `POST /api/users/login-csv`
Login all users from CSV and generate tokens.
```json
{
  "success": true,
  "message": "CSV login process completed",
  "summary": {
    "total": 10,
    "successful": 8,
    "failed": 2
  }
}
```

#### `POST /api/users/extract-tokens`
Extract tokens from CSV to JSON format.
```json
{
  "success": true,
  "message": "Token extraction completed",
  "tokensData": {
    "count": 8,
    "tokens": [...]
  }
}
```

#### `GET /api/users/tokens`
Get all user tokens data.
```json
{
  "success": true,
  "data": {
    "generated_at": "2024-01-15T10:30:00Z",
    "count": 8,
    "tokens": [
      {
        "user_id": "IU-USER001",
        "token": "eyJhbGciOiJIUzI1NiIs...",
        "login_time": "2024-01-15T10:25:00Z"
      }
    ]
  }
}
```

#### `GET /api/users/summary`
Get users login summary.
```json
{
  "success": true,
  "data": {
    "summary": {
      "total": 10,
      "withValidTokens": 8,
      "loginFailed": 2
    },
    "invalid_users": [...]
  }
}
```

#### `GET /api/users/csv-status`
Check status of CSV files and data.

### Test Execution

#### `POST /api/run-k6`
Enhanced K6 test runner with intelligent mode detection.

**Parameters:**
- `scenarios`: Array of scenario names to run
- `testMode`: "scenario" or "load"
- `loadConfig`: Custom load configuration (optional)
- `staticAuth`: Static auth for scenario mode (optional)

#### `GET /api/test-config`
Get current test configuration and available modes.

#### `GET /api/logs`
List all test log files.

#### `GET /api/logs/:filename`
Get specific log file content.

## 🧪 Test Modes

### Scenario Testing Mode
- **Purpose**: Single scenario validation
- **Auth**: Static user credentials from config
- **Users**: Single user (from config or API params)
- **Execution**: Simple iterations
- **Use Case**: Development, debugging, CI/CD

### Load Testing Mode  
- **Purpose**: Performance and load testing
- **Auth**: Dynamic user pool from CSV
- **Users**: Multiple users with rotation
- **Execution**: Ramping VUs with stages
- **Use Case**: Performance validation, stress testing

## 🔧 Configuration

### Load Test Configuration (config/config.js)
```javascript
"RAMP_CONFIG": {
  "executor": "ramping-vus",
  "startVUs": 0,
  "stages": [
    {"duration": "30s", "target": 2},
    {"duration": "1m", "target": 2}, 
    {"duration": "30s", "target": 0}
  ],
  "gracefulStop": "30s"
}
```

### Environment Variables
- `TEST_MODE`: "scenario" or "load"
- `USER_ID`: Static user ID for scenario mode
- `AUTH_TOKEN`: Static auth token for scenario mode
- `SCENARIOS`: Comma-separated list of scenarios

## 📊 Monitoring & Logs

### Log Files
- **Scenario logs**: `scenario-{scenarios}-{timestamp}.txt`
- **Load logs**: `load-{scenarios}-{timestamp}.txt`

### Metrics Extraction
The system automatically extracts key metrics:
- Check success rates
- Total iterations
- HTTP request counts
- Performance metrics

## 🛠️ Troubleshooting

### Common Issues

1. **"No users available" error**
   ```bash
   # Check CSV status
   curl http://localhost:3000/api/users/csv-status
   
   # Regenerate tokens if needed
   curl -X POST http://localhost:3000/api/users/login-csv
   ```

2. **"Value is not object coercible" error**
   - Ensure CSV file exists and has valid data
   - Check that tokens were generated successfully
   - Verify test mode is set correctly

3. **Load test not using CSV data**
   - Verify `testMode: "load"` is set
   - Check `TEST_MODE` environment variable
   - Ensure CSV file has valid tokens

### Debug Commands
```bash
# Check file status
curl http://localhost:3000/api/users/csv-status

# View latest log
curl http://localhost:3000/api/logs | jq '.files[0].name'

# Get specific log content  
curl http://localhost:3000/api/logs/{log-name}
```

## 🎯 Best Practices

1. **Always regenerate tokens** before load testing
2. **Use scenario mode** for development and debugging
3. **Use load mode** for performance validation
4. **Monitor log files** for detailed execution info
5. **Check CSV status** before running load tests
6. **Rotate user credentials** periodically for security

## 📈 Example Workflows

### Development Workflow
```bash
# 1. Test single scenario
curl -X POST http://localhost:3000/api/run-k6 \
  -d '{"scenarios": ["buyGoodMarketScenario"], "testMode": "scenario"}'

# 2. Check results
curl http://localhost:3000/api/logs
```

### Load Testing Workflow
```bash
# 1. Generate fresh tokens
curl -X POST http://localhost:3000/api/users/login-csv

# 2. Extract tokens
curl -X POST http://localhost:3000/api/users/extract-tokens

# 3. Run load test
curl -X POST http://localhost:3000/api/run-k6 \
  -d '{"scenarios": ["buyGoodMarketScenario"], "testMode": "load"}'

# 4. Monitor results
curl http://localhost:3000/api/logs/{log-name}
```
