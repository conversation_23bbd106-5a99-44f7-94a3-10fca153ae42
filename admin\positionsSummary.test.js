import http from 'k6/http';
import { group, check } from 'k6';
import {
  ADMIN_BASE,
  ADMIN_AUTH_TOKEN,
  OVERSEE_USER_ID, // AG-OKIDA6
} from '../config/config.js';

const validHeaders = {
  'Content-Type': 'application/json',
  Authorization: ADMIN_AUTH_TOKEN,
};

const badHeaders = {
  'Content-Type': 'application/json',
  Authorization: 'Bearer invalid-token',
};

const noAuthHeaders = {
  'Content-Type': 'application/json',
};

export const options = {
  vus: 1,
  iterations: 1,
};

export default function () {
  goodPositionsSummary();
  badPositionsSummary();
}

// ✅ GOOD FLOW
export function goodPositionsSummary() {
  group('✅ positionsSummary Good Flows', () => {
    // 1. Get Positions Summary
    const res1 = http.get(`${ADMIN_BASE}/getPositionsSummary/${OVERSEE_USER_ID}?page=1&page_size=10`, {
      headers: validHeaders,
    });

    const ok1 = check(res1, {
      'positionsSummary GET /getPositionsSummary → 200': (r) => r.status === 200,
      'positionsSummary GET /getPositionsSummary → has data': (r) => {
        try {
          const json = r.json();
          return Array.isArray(json.data) && typeof json.total_count === 'number';
        } catch (e) {
          console.error('positionsSummary GET /getPositionsSummary → JSON parse error:', e.message);
          return false;
        }
      },
    });

    if (!ok1) {
      console.error('positionsSummary GET /getPositionsSummary failed:', res1.status, res1.body);
    }

    // 2. Get Positions by Instrument
    const instrumentId = '1120250618445825';
    const res2 = http.get(`${ADMIN_BASE}/getPositionsSummaryByInstrument/${OVERSEE_USER_ID}/${instrumentId}?page=1&page_size=10`, {
      headers: validHeaders,
    });

    const ok2 = check(res2, {
      'positionsSummary GET /getPositionsByInstrument → 200': (r) => r.status === 200,
      'positionsSummary GET /getPositionsByInstrument → has data': (r) => {
        try {
          const json = r.json();
          return Array.isArray(json.data) && typeof json.total_count === 'number';
        } catch (e) {
          console.error('positionsSummary GET /getPositionsByInstrument → JSON parse error:', e.message);
          return false;
        }
      },
    });

    if (!ok2) {
      console.error('positionsSummary GET /getPositionsByInstrument failed:', res2.status, res2.body);
    }

    // 3. Get User Status
    const res3 = http.get(`${ADMIN_BASE}/user/status/${OVERSEE_USER_ID}`, {
      headers: validHeaders,
    });

    const ok3 = check(res3, {
      'positionsSummary GET /user/status → 200': (r) => r.status === 200,
      'positionsSummary GET /user/status → has is_enabled': (r) => {
        try {
          const json = r.json();
          return Array.isArray(json.is_enabled) || typeof json.is_enabled === 'boolean';
        } catch (e) {
          console.error('positionsSummary GET /user/status → JSON parse error:', e.message);
          return false;
        }
      },
    });

    if (!ok3) {
      console.error('positionsSummary GET /user/status failed:', res3.status, res3.body);
    }

    // 4. Get Position by Instrument + User
    const res4 = http.get(`${ADMIN_BASE}/getPositionsSummaryByInstrumentUser/${OVERSEE_USER_ID}/${instrumentId}?page=1&page_size=10`, {
      headers: validHeaders,
    });

    const ok4 = check(res4, {
      'positionsSummary GET /getPositionsByInstrumentUser → 200': (r) => r.status === 200,
      'positionsSummary GET /getPositionsByInstrumentUser → has data': (r) => {
        try {
          const json = r.json();
          return Array.isArray(json.data) && typeof json.total_count === 'number';
        } catch (e) {
          console.error('positionsSummary GET /getPositionsByInstrumentUser → JSON parse error:', e.message);
          return false;
        }
      },
    });

    if (!ok4) {
      console.error('positionsSummary GET /getPositionsByInstrumentUser failed:', res4.status, res4.body);
    }
  });
}

// ❌ BAD FLOW
export function badPositionsSummary() {
  group('❌ positionsSummary Bad Flows', () => {
    const badUserId = 'INVALID-ID';
    const badInstrumentId = 'BAD-INSTR';

    // Test helper function
    const test = (desc, url, headers, expectedStatus = 400) => {
      const res = http.get(url, { headers });
      const ok = check(res, {
        [`${desc} → ${expectedStatus}+`]: (r) => r.status >= expectedStatus,
      });
      if (!ok) {
        console.error(`${desc} failed →`, res.status, res.body);
      }
    };

    // Test invalid user IDs and instrument IDs - these return 200 with empty data
    const testInvalidData = (desc, url, headers) => {
      const res = http.get(url, { headers });
      const ok = check(res, {
        [`${desc} → 200`]: (r) => r.status === 200,
        [`${desc} → empty data`]: (r) => {
          try {
            const json = r.json();
            return Array.isArray(json.data) && json.data.length === 0;
          } catch (e) {
            console.error(`${desc} → JSON parse error:`, e.message);
            return false;
          }
        },
      });
      if (!ok) {
        console.error(`${desc} failed →`, res.status, res.body);
      }
    };

    testInvalidData(
      'positionsSummary GET /getPositionsSummary → invalid user_id',
      `${ADMIN_BASE}/getPositionsSummary/${badUserId}?page=1&page_size=10`,
      validHeaders
    );

    test(
      'positionsSummary GET /getPositionsByInstrument → invalid IDs',
      `${ADMIN_BASE}/getPositionsSummaryByInstrument/${badUserId}/${badInstrumentId}?page=1&page_size=10`,
      validHeaders
    );

    test(
      'positionsSummary GET /user/status → invalid user_id',
      `${ADMIN_BASE}/user/status/${badUserId}`,
      validHeaders
    );

    test(
      'positionsSummary GET /getPositionsByInstrumentUser → invalid IDs',
      `${ADMIN_BASE}/getPositionsSummaryByInstrumentUser/${badUserId}/${badInstrumentId}?page=1&page_size=10`,
      validHeaders
    );

    // Test missing authentication
    test(
      'positionsSummary GET /getPositionsSummary → no auth',
      `${ADMIN_BASE}/getPositionsSummary/${OVERSEE_USER_ID}?page=1&page_size=10`,
      noAuthHeaders,
      401
    );

    test(
      'positionsSummary GET /getPositionsByInstrument → no auth',
      `${ADMIN_BASE}/getPositionsSummaryByInstrument/${OVERSEE_USER_ID}/1120250618445825?page=1&page_size=10`,
      noAuthHeaders,
      401
    );

    test(
      'positionsSummary GET /user/status → no auth',
      `${ADMIN_BASE}/user/status/${OVERSEE_USER_ID}`,
      noAuthHeaders,
      401
    );

    // Test invalid authentication
    test(
      'positionsSummary GET /getPositionsSummary → bad token',
      `${ADMIN_BASE}/getPositionsSummary/${OVERSEE_USER_ID}?page=1&page_size=10`,
      badHeaders,
      401
    );

    test(
      'positionsSummary GET /getPositionsByInstrument → bad token',
      `${ADMIN_BASE}/getPositionsSummaryByInstrument/${OVERSEE_USER_ID}/1120250618445825?page=1&page_size=10`,
      badHeaders,
      401
    );

    test(
      'positionsSummary GET /user/status → bad token',
      `${ADMIN_BASE}/user/status/${OVERSEE_USER_ID}`,
      badHeaders,
      401
    );

    // Test missing page parameters - these return 200 with data (pagination optional)
    const testMissingPageParams = (desc, url, headers) => {
      const res = http.get(url, { headers });
      const ok = check(res, {
        [`${desc} → 200`]: (r) => r.status === 200,
        [`${desc} → has data`]: (r) => {
          try {
            const json = r.json();
            return Array.isArray(json.data) && typeof json.total_count === 'number';
          } catch (e) {
            console.error(`${desc} → JSON parse error:`, e.message);
            return false;
          }
        },
      });
      if (!ok) {
        console.error(`${desc} failed →`, res.status, res.body);
      }
    };

    testMissingPageParams(
      'positionsSummary GET /getPositionsSummary → missing page params',
      `${ADMIN_BASE}/getPositionsSummary/${OVERSEE_USER_ID}`,
      validHeaders
    );

    testMissingPageParams(
      'positionsSummary GET /getPositionsByInstrument → missing page params',
      `${ADMIN_BASE}/getPositionsSummaryByInstrument/${OVERSEE_USER_ID}/1120250618445825`,
      validHeaders
    );
  });
}
