import http from 'k6/http';
import Papa from 'https://jslib.k6.io/papaparse/5.1.1/index.js';
import { SharedArray } from 'k6/data';
import { BASE_URL } from '../config/config.js';

const csvPath = '../data/users.csv'; // Format: user_id,password

const users = new SharedArray('users', () => {
  const csv = open(csvPath);
  return Papa.parse(csv, { header: true }).data.filter(u => u.user_id && u.password);
});

const updatedRows = [];

export const options = {
  vus: 1,
  iterations: 1,
};

export default function loginFromCsv() {
  users.forEach(user => {
    const res = http.post(`${BASE_URL}/api/auth/login`, JSON.stringify({
      identifier: user.user_id,
      password: user.password,
    }), {
      headers: { 'Content-Type': 'application/json' },
    });

    let token = '';
    let newUserId = user.user_id;

    if (res.status === 200 && res.json('status') === 'success') {
      token = res.json('token.token');
      newUserId = res.json('user_id') || user.user_id;
      console.log(`Token fetched for ${newUserId}`);
    } else {
      console.error(`Login failed for ${user.user_id}`);
    }

    updatedRows.push({
      user_id: newUserId,
      password: user.password,
      token,
    });
  });

  // Final log: CSV output with NO emojis or special chars
  console.log('');
  console.log('COPY BELOW CONTENT TO: /data/users_with_tokens.csv');
  console.log('user_id,password,token');
  updatedRows.forEach(u => {
    console.log(`${u.user_id},${u.password},${u.token}`);
  });
}
