$logPath = "logs/createUser-7.log"
$csvPath = "data/users.csv"


# Ensure the output list
$output = @()

# Read and process each line
Get-Content $logPath | ForEach-Object {
    if ($_ -match 'msg="(.+)"') {
        $escapedJson = $matches[1]
        # Unescape inner quotes
        $json = $escapedJson -replace '\\"', '"'
        try {
            $obj = $json | ConvertFrom-Json
            $output += [PSCustomObject]@{
                user_id  = $obj.user_id
                password = $obj.password
                email    = $obj.email
            }
        } catch {
            Write-Warning "Failed to parse: $json"
        }
    }
}

# Export to CSV
if ($output.Count -gt 0) {
    $output | Export-Csv -Path $csvPath -NoTypeInformation
    Write-Host "`n✅ Saved CSV to: $csvPath`n"
} else {
    Write-Warning "No valid JSON entries found in log."
}
