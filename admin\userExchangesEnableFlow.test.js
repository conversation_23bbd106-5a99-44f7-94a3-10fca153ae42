import http from 'k6/http';
import { check, group, sleep } from 'k6';
import * as cfg from '../config/config.js';
import { SharedArray } from 'k6/data';
import Papa from 'https://jslib.k6.io/papaparse/5.1.1/index.js';

const BAD_JWT = 'Bearer bad.token.value';
const users = new SharedArray('users', () => {
  const csv = open('../data/users.csv');
  return Papa.parse(csv, { header: true }).data.filter(u => u.user_id);
});

const headers = (token) => ({
  accept: 'application/json',
  'Content-Type': 'application/json',
  Authorization: token,
});

const ALL_EXCHANGES = {
  NSE: true,
  CRYPTO: true,
  NASDAQ: true,
  MCX: true,
};

export const options = {
  scenarios: {
    exchange_enablement_flow: {
      executor: 'per-vu-iterations',
      vus: users.length,
      iterations: 1,
      exec: 'positiveExchangeEnableFlow',
    },
    exchange_enablement_negative: {
      executor: 'per-vu-iterations',
      vus: 1,
      iterations: 1,
      exec: 'negativeExchangeEnableCases',
    },
  },
};

// ✅ Positive test for each user
export function positiveExchangeEnableFlow() {
  const user = users[__ITER];
  group(`✅ Exchange Enablement Flow → ${user.user_id}`, () => {
    const getRes = http.get(`${cfg.ADMIN_BASE}/user/exchanges/${user.user_id}`, {
      headers: headers(cfg.ADMIN_AUTH_TOKEN),
    });

    const getOk = check(getRes, {
      'userExchanges GET user/exchanges → 200': (r) => r.status === 200,
    });

    if (!getOk) {
      console.error('userExchanges GET failed:', getRes.status, getRes.body);
      return;
    }

    console.log(`userExchanges Before: ${JSON.stringify(getRes.json())}`);

    const postRes = http.post(`${cfg.ADMIN_BASE}/user/exchanges`, JSON.stringify({
      user_id: user.user_id,
      list: ALL_EXCHANGES,
    }), { headers: headers(cfg.ADMIN_AUTH_TOKEN) });

    const postOk = check(postRes, {
      'userExchanges POST user/exchanges → 200': (r) => r.status === 200,
      'userExchanges POST → success msg': (r) => r.json()?.message?.includes('updated'),
    });

    if (!postOk) {
      console.error('userExchanges POST failed:', postRes.status, postRes.body);
      return;
    }

    const confirmRes = http.get(`${cfg.ADMIN_BASE}/user/exchanges/${user.user_id}`, {
      headers: headers(cfg.ADMIN_AUTH_TOKEN),
    });

    const confirmOk = check(confirmRes, {
      'userExchanges Confirm GET → 200': (r) => r.status === 200,
      'userExchanges All exchanges enabled': (r) => {
        const j = r.json();
        return j.NSE && j.CRYPTO && j.MCX && j.NASDAQ;
      },
    });

    if (!confirmOk) {
      console.error('userExchanges Confirm failed:', confirmRes.status, confirmRes.body);
    }
  });

  sleep(1);
}

// ❌ Negative scenarios for GET and POST
export function negativeExchangeEnableCases() {
  group('❌ NEGATIVE: GET /user/exchanges/:userId', () => {
    const cases = [
      { label: 'userExchanges No JWT', url: `${cfg.ADMIN_BASE}/user/exchanges/IU-123`, token: '', exp: 403 },
      { label: 'userExchanges Bad JWT', url: `${cfg.ADMIN_BASE}/user/exchanges/IU-123`, token: BAD_JWT, exp: 403 },
      { label: 'userExchanges Invalid ID', url: `${cfg.ADMIN_BASE}/user/exchanges/IU-NOPE`, token: cfg.ADMIN_AUTH_TOKEN, exp: 200 }, // Returns 200, warn
    ];

    for (const { label, url, token, exp } of cases) {
      const res = http.get(url, { headers: headers(token) });
      const ok = check(res, { [`${label} → ${exp}`]: r => r.status === exp });
      if (!ok) {
        console.error(`[${label}] expected ${exp}, got ${res.status} → ${res.body}`);
      }
    }
  });

  group('❌ NEGATIVE: POST /user/exchanges', () => {
    const base = {
      user_id: 'IU-123',
      list: { NSE: true, CRYPTO: true, NASDAQ: true, MCX: true },
    };

    const cases = [
      { label: 'userExchanges No JWT', body: base, token: '', exp: 403 },
      { label: 'userExchanges Bad JWT', body: base, token: BAD_JWT, exp: 403 },
      { label: 'userExchanges Missing list', body: { user_id: 'IU-123' }, exp: 422 },
      { label: 'userExchanges Missing user_id', body: { list: base.list }, exp: 422 },
      { label: 'userExchanges Empty payload', body: {}, exp: 422 },
      { label: 'userExchanges Fake user_id', body: { ...base, user_id: 'IU-NOPE' }, exp: 200 }, // Accepts unknown user, warn
      { label: 'userExchanges List value not boolean (no validation)', body: { user_id: 'IU-123', list: { NSE: "yes" } }, exp: 200 },
    ];

    for (const { label, body, token = cfg.ADMIN_AUTH_TOKEN, exp } of cases) {
      const res = http.post(`${cfg.ADMIN_BASE}/user/exchanges`, JSON.stringify(body), {
        headers: headers(token),
      });

      const ok = check(res, { [`${label} → ${exp}`]: (r) => r.status === exp });

      if (!ok) {
        console.error(`[${label}] expected ${exp}, got ${res.status}`);
        console.error('Payload:', JSON.stringify(body));
        console.error('Response:', res.body);
      }
    }
  });

  sleep(1);
}
