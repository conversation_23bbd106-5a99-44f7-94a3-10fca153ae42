import { sleep, check, group } from 'k6';
import http from 'k6/http';
import { BASE_URL, ADMIN_BASE, EXCHANGE } from '../config/config.js';
import { getUserSafe, safeJson } from '../utils/csvUserManager.js';

export const options = {
  vus: 1,
  iterations: 1,
};

export function sellGoodStopLossOrderFlow() {
  group('✅ Sell Stop Loss Order - Good Flow', () => {
    const { user_id, password, token } = getUserSafe();

    if (!user_id || !token) {
      console.error(`❌ sellGoodStopLossOrderFlow: No valid user available for VU ${__VU}`);
      return;
    }

    const headers = {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    };

    // 1. Wallet BEFORE
    const walletBeforeRes = http.get(`${ADMIN_BASE}/wallets/balance/${user_id}`, { headers });
    const walletBeforeOk = check(walletBeforeRes, {
      'sellStopLossOrder GET wallet before → 200': (r) => r.status === 200,
      'sellStopLossOrder GET wallet before → has wallets': (r) => {
        try {
          const json = r.json();
          return Array.isArray(json.wallets) && json.wallets.length > 0;
        } catch (e) {
          console.error('sellStopLossOrder GET wallet before → JSON parse error:', e.message);
          return false;
        }
      },
    });

    if (!walletBeforeOk) {
      console.error('sellStopLossOrder GET wallet before failed:', walletBeforeRes.status, walletBeforeRes.body);
      return;
    }

    const walletBeforeJson = safeJson(walletBeforeRes);
    const netBefore = parseFloat(walletBeforeJson?.wallets?.find(w => w.exchange === EXCHANGE)?.net_balance || 0);

    if (netBefore <= 0) {
      console.error(`sellStopLossOrder: Insufficient balance (${netBefore}) for user ${user_id}`);
      return;
    }

    // 2. Instrument
    const instrumentRes = http.post(`${BASE_URL}/api/user/getInstruments`, JSON.stringify({
      exchange: EXCHANGE,
      searchPattern: 't',
      page: 1,
      pageSize: 1,
    }), { headers });

    const instOk = check(instrumentRes, {
      'sellStopLossOrder POST getInstruments → 200': (r) => r.status === 200,
      'sellStopLossOrder POST getInstruments → has instruments': (r) => {
        try {
          const json = r.json();
          return Array.isArray(json.instruments) && json.instruments.length > 0;
        } catch (e) {
          console.error('sellStopLossOrder POST getInstruments → JSON parse error:', e.message);
          return false;
        }
      },
    });

    if (!instOk) {
      console.error('sellStopLossOrder POST getInstruments failed:', instrumentRes.status, instrumentRes.body);
      return;
    }

    const instrumentJson = safeJson(instrumentRes);
    const instrument = instrumentJson?.instruments?.[0];
    if (!instrument) {
      console.error("sellStopLossOrder POST getInstruments: No instrument found");
      return;
    }

    const {
      FinancialInstrumentID: instrumentId,
      TradingSymbol: symbol,
      LastPrice: lastPrice,
      UpperCkt: upperCkt,
    } = instrument;

    const triggerPrice = parseFloat((lastPrice * 1.03).toFixed(2));

    // Validate instrument pricing data
    if (!lastPrice || lastPrice === 0 || isNaN(lastPrice)) {
      console.error(`sellStopLossOrder: Invalid instrument data → LastPrice: ${lastPrice}`);
      return;
    }

    // Check if upper circuit is valid, if not, use a safe approach
    if (!upperCkt || upperCkt === 0 || isNaN(upperCkt)) {
      console.error(`sellStopLossOrder: Invalid instrument data → LastPrice: ${lastPrice}, UpperCkt: ${upperCkt}`);
      return;
    }

    // Validate price boundaries
    if (triggerPrice >= upperCkt) {
      console.error(`sellStopLossOrder: triggerPrice (${triggerPrice}) ≥ upper circuit (${upperCkt})`);
      return;
    }

    // 3. Place STOPLOSS order
    const orderPayload = {
      instrumentId,
      exchange: EXCHANGE,
      quantity: 1,
      OrderType: 'STOPLOSS',
      buy: false,
      triggerPrice,
    };
    const orderRes = http.post(`${BASE_URL}/api/user/makeOrder`, JSON.stringify(orderPayload), { headers });
    const orderOk = check(orderRes, {
      'sellStopLossOrder POST makeOrder → 200': (r) => r.status === 200,
      'sellStopLossOrder POST makeOrder → success': (r) => {
        try {
          const json = r.json();
          return json?.status === 'success';
        } catch (e) {
          console.error('sellStopLossOrder POST makeOrder → JSON parse error:', e.message);
          return false;
        }
      },
      'sellStopLossOrder POST makeOrder → funds blocked': (r) => {
        try {
          const json = r.json();
          const fundsMatch = json?.message?.match(/Funds blocked: ([\d.]+)/);
          return fundsMatch && parseFloat(fundsMatch[1]) > 0;
        } catch (e) {
          console.error('sellStopLossOrder POST makeOrder → funds check error:', e.message);
          return false;
        }
      },
    });

    if (!orderOk) {
      console.error('sellStopLossOrder POST makeOrder failed:', orderRes.status, orderRes.body);
      console.error('sellStopLossOrder POST makeOrder payload:', JSON.stringify(orderPayload, null, 2));
      return;
    }

    sleep(1);

    // 4. Wallet AFTER
    const walletAfterRes = http.get(`${ADMIN_BASE}/wallets/balance/${user_id}`, { headers });
    const walletAfterOk = check(walletAfterRes, { 'sellStopLossOrder_wallet after → 200': (r) => r.status === 200 });
    if (!walletAfterOk) console.error('sellStopLossOrder_wallet After:', walletAfterRes.status, walletAfterRes.body);

    const walletAfterJson = safeJson(walletAfterRes);
    const netAfter = parseFloat(walletAfterJson?.wallets?.find(w => w.exchange === EXCHANGE)?.net_balance || 0);

    // 5. Portfolio logic (long cover check)
    const portfolioRes = http.post(`${BASE_URL}/api/user/getPortfolio`, JSON.stringify({ exchange: EXCHANGE }), { headers });
    const portfolioOk = check(portfolioRes, { 'sellStopLossOrder_getPortfolio → 200': (r) => r.status === 200 });
    if (!portfolioOk) console.error('sellStopLossOrder_getPortfolio:', portfolioRes.status, portfolioRes.body);

    const portfolioJson = safeJson(portfolioRes);
    const position = portfolioJson?.portfolios?.find(p => p.FinancialInstrumentID === instrumentId);
    const isLongCovered = position && position.Quantity > 0;
    console.log(`📊 Position check: Qty = ${position?.Quantity ?? 'N/A'}, isLong = ${isLongCovered}`);

    check(null, {
      [`net_balance ${isLongCovered ? 'unchanged (long cover)' : 'reduced'} after STOPLOSS`]: () =>
        isLongCovered ? netAfter === netBefore : netAfter < netBefore,
    });

    // 6. openOrders check
    const openRes = http.post(`${BASE_URL}/api/user/openOrders`, JSON.stringify({ exchange: EXCHANGE }), { headers });
    const openOk = check(openRes, { 'sellStopLossOrder_openOrders → 200': (r) => r.status === 200 });
    if (!openOk) console.error('sellStopLossOrder_openOrders:', openRes.status, openRes.body);

    const openJson = safeJson(openRes);
    const order = openJson?.orders?.find(o => o.instrumentId === instrumentId && o.orderType === 'STOPLOSS');
    if (!order) {
      console.error("sellStopLossOrder STOPLOSS order not found in openOrders");
      return;
    }

    const orderId = order.ID;

    // 7. Modify
    const modifyPayload = {
      exchange: EXCHANGE,
      orderId,
      triggerPrice: triggerPrice + 1,
      quantity: 2,
    };
    const modifyRes = http.post(`${BASE_URL}/api/user/modifyOrder`, JSON.stringify(modifyPayload), { headers });
    const modifyOk = check(modifyRes, {
      'sellStopLossOrder_modifyOrder → 200': (r) => r.status === 200,
      'sellStopLossOrder_modifyOrder → success': (r) => r.json()?.status === 'success',
    });
    if (!modifyOk) {
      console.error('sellStopLossOrder_modifyOrder Payload:', JSON.stringify(modifyPayload, null, 2));
      console.error('sellStopLossOrder_modifyOrder Response:', modifyRes.status, modifyRes.body);
      return;
    }

    // 8. Cancel
    const cancelPayload = { exchange: EXCHANGE, orderId };
    const cancelRes = http.post(`${BASE_URL}/api/user/cancelOrder`, JSON.stringify(cancelPayload), { headers });
    const cancelOk = check(cancelRes, {
      'sellStopLossOrder_cancelOrder → 200': (r) => r.status === 200,
      'sellStopLossOrder_cancelOrder → success': (r) => r.json()?.status === 'success',
    });
    if (!cancelOk) {
      console.error('sellStopLossOrder_cancelOrder Payload:', JSON.stringify(cancelPayload, null, 2));
      console.error('sellStopLossOrder_cancelOrder Response:', cancelRes.status, cancelRes.body);
    }

    sleep(1);
  });
}



export function sellBadStopLossOrderFlow() {
  group('❌ Sell Stop Loss Order - Bad Scenarios', () => {
    const { user_id, password, token } = getUserSafe();

    if (!user_id || !token) {
      console.error(`❌ sellBadStopLossOrderFlow: No valid user available for VU ${__VU}`);
      return;
    }

    const headers = {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    };

    const badHeaders = {
      'Content-Type': 'application/json',
      Authorization: 'Bearer invalid-token',
    };

    const noAuthHeaders = {
      'Content-Type': 'application/json',
    };

    // Test helper function
    const test = (desc, url, payload, testHeaders, expectedStatus = 400) => {
      const res = http.post(url, payload, { headers: testHeaders });
      const ok = check(res, {
        [`${desc} → ${expectedStatus}+`]: (r) => r.status >= expectedStatus,
      });
      if (!ok) {
        console.error(`${desc} failed →`, res.status, res.body);
      }
    };

    const invalidId = 999999999999;

    // Test missing triggerPrice
    test(
      'sellStopLossOrder POST makeOrder → missing triggerPrice',
      `${BASE_URL}/api/user/makeOrder`,
      JSON.stringify({
        instrumentId: invalidId,
        exchange: EXCHANGE,
        quantity: 1,
        OrderType: 'STOPLOSS',
        buy: false,
      }),
      headers
    );

    // Test invalid instrument ID
    test(
      'sellStopLossOrder POST makeOrder → invalid instrumentId',
      `${BASE_URL}/api/user/makeOrder`,
      JSON.stringify({
        instrumentId: invalidId,
        exchange: EXCHANGE,
        quantity: 1,
        OrderType: 'STOPLOSS',
        buy: false,
        triggerPrice: 9999,
      }),
      headers
    );

    // Test invalid quantity
    test(
      'sellStopLossOrder POST makeOrder → invalid quantity',
      `${BASE_URL}/api/user/makeOrder`,
      JSON.stringify({
        instrumentId: 1,
        exchange: EXCHANGE,
        quantity: -1,
        OrderType: 'STOPLOSS',
        buy: false,
        triggerPrice: 100,
      }),
      headers
    );

    // Test malformed JSON
    test(
      'sellStopLossOrder POST makeOrder → malformed JSON',
      `${BASE_URL}/api/user/makeOrder`,
      '{"instrumentId": 1, "exchange": "' + EXCHANGE + '", "quantity": invalid json',
      headers
    );

    // Test missing authentication
    test(
      'sellStopLossOrder POST makeOrder → no auth',
      `${BASE_URL}/api/user/makeOrder`,
      JSON.stringify({
        instrumentId: 1,
        exchange: EXCHANGE,
        quantity: 1,
        OrderType: 'STOPLOSS',
        buy: false,
        triggerPrice: 100,
      }),
      noAuthHeaders,
      401
    );

    // Test invalid authentication
    test(
      'sellStopLossOrder POST makeOrder → bad token',
      `${BASE_URL}/api/user/makeOrder`,
      JSON.stringify({
        instrumentId: 1,
        exchange: EXCHANGE,
        quantity: 1,
        OrderType: 'STOPLOSS',
        buy: false,
        triggerPrice: 100,
      }),
      badHeaders,
      401
    );

    // Test getInstruments with bad auth
    test(
      'sellStopLossOrder POST getInstruments → no auth',
      `${BASE_URL}/api/user/getInstruments`,
      JSON.stringify({ exchange: EXCHANGE, searchPattern: 't', page: 1, pageSize: 1 }),
      noAuthHeaders,
      401
    );

    test(
      'sellStopLossOrder POST getInstruments → bad token',
      `${BASE_URL}/api/user/getInstruments`,
      JSON.stringify({ exchange: EXCHANGE, searchPattern: 't', page: 1, pageSize: 1 }),
      badHeaders,
      401
    );

    sleep(1);
  });
}

export default function () {
  sellGoodStopLossOrderFlow();
  sellBadStopLossOrderFlow();
}

