import { SharedArray } from 'k6/data';
import Papa from 'https://jslib.k6.io/papaparse/5.1.1/index.js';

// Get test mode from environment variable
const TEST_MODE = __ENV.TEST_MODE || 'load'; // 'load' or 'scenario'

// Configuration based on test mode
const getConfig = () => {
  if (TEST_MODE === 'scenario') {
    // For scenario testing, use static config values
    return {
      useStaticAuth: true,
      USER_ID: __ENV.USER_ID || 'default-user',
      AUTH_TOKEN: __ENV.AUTH_TOKEN || 'default-token'
    };
  } else {
    // For load testing, use CSV data
    return {
      useStaticAuth: false,
      csvPath: '../data/users_with_tokens.csv'
    };
  }
};

const config = getConfig();

// Shared CSV user loading utility (only load if needed for load testing)
export const users = config.useStaticAuth ? [] : new SharedArray('users', () => {
  try {
    const raw = open('../data/users_with_tokens.csv');
    
    if (!raw) {
      console.error('❌ Failed to read CSV file: ../data/users_with_tokens.csv');
      return [];
    }

    // Parse CSV directly
    const parseResult = Papa.parse(raw, { header: true });

    const parsedUsers = parseResult.data
      .filter(u => {
        // Handle BOM character in field names
        const userId = u.user_id || u['﻿user_id'];
        const token = u.token;
        return userId && token && userId.trim() && token.trim();
      })
      .map(u => {
        // Handle BOM character in field names
        const userId = u.user_id || u['﻿user_id'];
        return {
          user_id: userId.trim(),
          password: u.password ? u.password.trim() : '',
          token: u.token.replace(/\s/g, ''), // remove all whitespace (linebreaks, spaces, tabs)
        };
      });
    
    console.log(`✅ Loaded ${parsedUsers.length} users from CSV`);
    return parsedUsers;
  } catch (error) {
    console.error('❌ Error loading users from CSV:', error);
    return [];
  }
});

// Provide one user per VU with error handling
export function getUser() {
  // For scenario testing, return static user
  if (config.useStaticAuth) {
    console.log(`✅ Using static auth for scenario testing (VU: ${__VU})`);
    return {
      user_id: config.USER_ID,
      password: 'static-password', // Not used in scenario mode
      token: config.AUTH_TOKEN
    };
  }

  // For load testing, use CSV data
  if (!users || users.length === 0) {
    console.error('❌ No users available. Check CSV file loading.');
    return null;
  }

  const userIndex = (__VU - 1) % users.length;
  const user = users[userIndex];

  if (!user) {
    console.error(`❌ No user found at index ${userIndex}. VU: ${__VU}, Total users: ${users.length}`);
    return null;
  }

  console.log(`✅ Using CSV user: ${user.user_id} (VU: ${__VU}, Mode: ${TEST_MODE})`);
  return user;
}

// Get token for a specific user
export function getToken(user_id, _password) {
  const user = users.find(u => u.user_id === user_id);
  return user?.token || null;
}

// Safe JSON parsing utility
export function safeJson(response) {
  try {
    return response.json();
  } catch (error) {
    console.error('Failed to parse JSON:', error, 'Response body:', response.body);
    return {};
  }
}

// Get user with validation for destructuring
export function getUserSafe() {
  const user = getUser();

  if (!user) {
    console.error(`❌ No user available for VU ${__VU} in ${TEST_MODE} mode`);
    return { user_id: null, password: null, token: null };
  }

  const { user_id, password, token } = user;

  if (!user_id || !token) {
    console.error(`❌ Missing user_id or token for VU ${__VU} in ${TEST_MODE} mode:`, user);
    return { user_id: null, password: null, token: null };
  }

  return { user_id, password, token };
}

// Get test mode information
export function getTestMode() {
  return {
    mode: TEST_MODE,
    useStaticAuth: config.useStaticAuth,
    userCount: config.useStaticAuth ? 1 : (users ? users.length : 0)
  };
}
