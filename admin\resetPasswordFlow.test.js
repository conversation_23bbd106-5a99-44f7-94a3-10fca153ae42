import http from 'k6/http';
import { check, group } from 'k6';
import { ADMIN_BASE, ADMIN_AUTH_TOKEN, ADMIN_EMAIL, ADMIN_PASSWORD, OVERSEE_USER_ID } from '../config/config.js';

const headers = {
  'Content-Type': 'application/json',
  Authorization: ADMIN_AUTH_TOKEN,
};

export const options = {
  vus: 1,
  iterations: 1,
};

export default function () {
  let newPassword = null;

  group('resetPasswordFlow Step 1: Reset password (PUT /user/resetPassword)', () => {
    const resetPayload = JSON.stringify({ user_id: OVERSEE_USER_ID });
    const res = http.put(`${ADMIN_BASE}/user/resetPassword`, resetPayload, { headers });

    const ok = check(res, {
      'resetPasswordFlow PUT /user/resetPassword → 200': (r) => r.status === 200,
      'resetPasswordFlow PUT /user/resetPassword → returns new_password': (r) => !!r.json().new_password,
    });

    if (!ok) {
      console.error('resetPasswordFlow Failed to reset password:', res.status, res.body);
      return;
    }

    newPassword = res.json().new_password;
  });

  group('resetPasswordFlow Step 2: Login with new password (POST /login)', () => {
    const loginPayload = JSON.stringify({
      email: ADMIN_EMAIL,
      password: newPassword,
    });

    const res = http.post(`${ADMIN_BASE}/login`, loginPayload, {
      headers: { 'Content-Type': 'application/json' },
    });

    const ok = check(res, {
      'resetPasswordFlow POST /login (with new password) → 200': (r) => r.status === 200,
      'resetPasswordFlow POST /login → returns success': (r) => r.json().status === 'success',
      'resetPasswordFlow POST /login → returns token': (r) => !!r.json().token?.token,
    });

    if (!ok) {
      console.error('resetPasswordFlow Failed to login with new password:', res.status, res.body);
      return;
    }
  });

  group('🔁 Step 3: Update password back to original (POST /update-password-userid)', () => {
    const updatePayload = JSON.stringify({
      userId: OVERSEE_USER_ID,
      current_password: newPassword,
      new_password: ADMIN_PASSWORD,
    });

    const res = http.post(`${ADMIN_BASE}/update-password-userid`, updatePayload, { headers });

    const ok = check(res, {
      'resetPasswordFlow POST /update-password-userid → 200': (r) => r.status === 200,
      'resetPasswordFlow POST /update-password-userid → success message': (r) =>
        r.json().message === 'Password updated successfully',
    });

    if (!ok) {
      console.error('resetPasswordFlow Failed to update password back to original:', res.status, res.body);
    }
  });
}
