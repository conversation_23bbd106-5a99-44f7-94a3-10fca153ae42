import http from 'k6/http';
import { check, group } from 'k6';
import { ADMIN_BASE, ADMIN_AUTH_TOKEN, USER_ID } from '../config/config.js';

const headers = {
  'Authorization': ADMIN_AUTH_TOKEN,
  'Content-Type': 'application/json',
};

export const options = {
  vus: 1,
  iterations: 1,
};

export default function () {
  goodUserTransactions();
  badUserTransactions();
}

export function goodUserTransactions() {
  group('✅ User Transactions - Good Flow', () => {
    const res = http.get(`${ADMIN_BASE}/transactions/${USER_ID}?page=1&page_size=10`, { headers });

    const ok = check(res, {
      'userTransactions GET /transactions → 200': (r) => r.status === 200,
      'userTransactions GET /transactions → has data': (r) => {
        try {
          const json = r.json();
          return json.data && Array.isArray(json.data);
        } catch (e) {
          console.error('userTransactions GET /transactions → JSON parse error:', e.message);
          return false;
        }
      },
      'userTransactions GET /transactions → valid response structure': (r) => {
        try {
          const json = r.json();
          return json.data !== undefined && json.count !== undefined;
        } catch (e) {
          console.error('userTransactions GET /transactions → response structure error:', e.message);
          return false;
        }
      },
    });

    if (!ok) {
      console.error('userTransactions GET /transactions failed:', res.status, res.body);
    }
  });
}

export function badUserTransactions() {
  group('❌ User Transactions - Bad Scenarios', () => {
    const fakeId = 'BAD-USER';

    const test = (desc, url, hdrs, expectedStatus = 400) => {
      const res = http.get(url, { headers: hdrs });
      const ok = check(res, { [`${desc} → ${expectedStatus}+`]: (r) => r.status >= expectedStatus });
      if (!ok) {
        console.error(`${desc} failed →`, res.status, res.body);
      }
    };

    // Test helper function for permissive API behavior
    const testPermissive = (desc, url, hdrs, expectedStatus = 400) => {
      const res = http.get(url, { headers: hdrs });
      const ok = check(res, {
        [`${desc} → ${expectedStatus}+ or 200 (API permissive)`]: (r) => {
          if (r.status >= expectedStatus) {
            return true; // Expected error status
          } else if (r.status === 200) {
            // API is permissive - check for empty data response
            try {
              const json = r.json();
              return Array.isArray(json.data) && json.data.length === 0 && json.count === 0;
            } catch (e) {
              return false;
            }
          }
          return false;
        },
      });
      if (!ok) {
        console.error(`${desc} failed →`, res.status, res.body);
      }
    };

    // Test helper function for API that returns data even without query params
    const testOptionalParams = (desc, url, hdrs) => {
      const res = http.get(url, { headers: hdrs });
      const ok = check(res, {
        [`${desc} → 200 (API accepts missing params)`]: (r) => {
          if (r.status === 200) {
            // API accepts missing params and returns data
            try {
              const json = r.json();
              return json.data && Array.isArray(json.data);
            } catch (e) {
              return false;
            }
          }
          return r.status >= 400; // Or returns error as expected
        },
      });
      if (!ok) {
        console.error(`${desc} failed →`, res.status, res.body);
      }
    };

    test('userTransactions GET /transactions → missing token', `${ADMIN_BASE}/transactions/${USER_ID}?page=1&page_size=10`, { 'Content-Type': 'application/json' }, 403);
    test('userTransactions GET /transactions → bad token', `${ADMIN_BASE}/transactions/${USER_ID}?page=1&page_size=10`, {
      ...headers,
      Authorization: 'Bearer fake-token',
    }, 401);
    testPermissive('userTransactions GET /transactions → invalid userId', `${ADMIN_BASE}/transactions/${fakeId}?page=1&page_size=10`, headers);
    testOptionalParams('userTransactions GET /transactions → missing query params', `${ADMIN_BASE}/transactions/${USER_ID}`, headers);
  });
}

// Legacy function names for backward compatibility
export const goodUserTransactionsCase = goodUserTransactions;
export const badUserTransactionsCases = badUserTransactions;
