import { sleep, check, group } from 'k6';
import http from 'k6/http';
import { ADMIN_BASE, USER_ID, EXCHANGE, AUTH_TOKEN } from '../config/config.js';
const walletHeaders = {
  accept: 'application/json',
  Authorization: AUTH_TOKEN,
};

export const options = {
  vus: 1,
  iterations: 1,
};

const badHeaders = {
  'Content-Type': 'application/json',
  Authorization: 'Bearer invalid-token',
};

const noAuthHeaders = {
  'Content-Type': 'application/json',
};

export function goodWallets() {
  group('✅ Wallet Balance - Good Flow', () => {
    const res = http.get(`${ADMIN_BASE}/wallets/balance/${USER_ID}`, { headers: walletHeaders });

    const ok = check(res, {
      'wallets GET /wallets/balance → 200': (r) => r.status === 200,
      'wallets GET /wallets/balance → has wallets': (r) => {
        try {
          const json = r.json();
          return Array.isArray(json.wallets) && json.wallets.length > 0;
        } catch (e) {
          console.error('wallets GET /wallets/balance → JSON parse error:', e.message);
          return false;
        }
      },
      'wallets GET /wallets/balance → has total_net_balance': (r) => {
        try {
          const json = r.json();
          return typeof json.total_net_balance === 'number' && json.total_net_balance >= 0;
        } catch (e) {
          console.error('wallets GET /wallets/balance → total_net_balance check error:', e.message);
          return false;
        }
      },
    });

    if (!ok) {
      console.error('wallets GET /wallets/balance failed:', res.status, res.body);
      return;
    }

    let data;
    try {
      data = res.json();
    } catch (err) {
      console.error("wallets GET /wallets/balance → Failed to parse JSON:", err.message);
      return;
    }

    if (!data?.wallets) {
      console.error("wallets GET /wallets/balance → 'wallets' field missing in response:", JSON.stringify(data));
      return;
    }

    const mcx = data.wallets.find((w) => w.exchange === EXCHANGE);
    if (mcx) {
      const mcxOk = check(mcx, {
        'wallets GET /wallets/balance → MCX balance >= 0': (w) => typeof w.balance === 'number' && w.balance >= 0,
        'wallets GET /wallets/balance → MCX usedMargin >= 0': (w) => typeof w.usedMargin === 'number' && w.usedMargin >= 0,
        'wallets GET /wallets/balance → MCX status exists': (w) => typeof w.status === 'boolean',
      });

      if (!mcxOk) {
        console.error(`wallets GET /wallets/balance → MCX validation failed:`, mcx);
      }

      console.log(`💰 ${EXCHANGE} → Balance: ${mcx.balance}, Used: ${mcx.usedMargin}, Available: ${mcx.availableMargin}`);
    } else {
      console.log(`⚠️ ${EXCHANGE} wallet not found in response`);
    }

    console.log(`🌐 Total Net Balance: ${data.total_net_balance}`);
    sleep(1);
  });
}

export function badWallets() {
  group('❌ Wallet Balance - Bad Scenarios', () => {

    // Test helper function
    const test = (desc, url, headers, expectedStatus = 400) => {
      const res = http.get(url, { headers });
      const ok = check(res, {
        [`${desc} → ${expectedStatus}+`]: (r) => r.status >= expectedStatus,
      });
      if (!ok) {
        console.error(`${desc} failed →`, res.status, res.body);
      }
    };

    // Test invalid user ID
    test(
      'wallets GET /wallets/balance → invalid user_id',
      `${ADMIN_BASE}/wallets/balance/INVALID-USER-ID`,
      walletHeaders
    );

    // Test missing authentication
    test(
      'wallets GET /wallets/balance → no auth',
      `${ADMIN_BASE}/wallets/balance/${USER_ID}`,
      noAuthHeaders,
      401
    );

    // Test invalid authentication
    test(
      'wallets GET /wallets/balance → bad token',
      `${ADMIN_BASE}/wallets/balance/${USER_ID}`,
      badHeaders,
      401
    );

    // Test non-existent endpoint
    test(
      'wallets GET /invalid-endpoint → 404',
      `${ADMIN_BASE}/invalid-endpoint/${USER_ID}`,
      walletHeaders,
      404
    );

    sleep(1);
  });
}

export default function () {
  goodWallets();
  badWallets();
}

