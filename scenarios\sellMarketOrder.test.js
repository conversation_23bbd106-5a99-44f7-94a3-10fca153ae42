import { sleep, check, group } from 'k6';
import http from 'k6/http';
import { BASE_URL, ADMIN_BASE, EXCHANGE } from '../config/config.js';
import { getUserSafe, safeJson } from '../utils/csvUserManager.js';



export const options = {
  scenarios: {
    sell_good_scenario_load: {
      executor: 'per-vu-iterations',
      vus: 1,
      iterations: 1,
      exec: 'sellGoodMarketOrder',
    },
    sell_bad_scenarios_once: {
      executor: 'per-vu-iterations',
      vus: 1,
      iterations: 1,
      exec: 'sellBadMarketOrder',
    },
  },
  thresholds: {
    'http_req_duration{scenario:good_scenario_load}': ['p(95)<1000'],
    'http_req_failed{scenario:good_scenario_load}': ['rate<0.01'],
  },
};

// ✅ Good Market Order Scenario (Load Tested)
export function sellGoodMarketOrder() {
  const { user_id, password, token } = getUserSafe();

  if (!user_id || !token) {
    console.error(`❌ No valid user available for VU ${__VU}`);
    return;
  }

  const headers = {
    'Content-Type': 'application/json',
    Authorization: `Bearer ${token}`,
  };

  group(`✅ Sell Market Order for ${user_id}`, () => {
    // 1. Wallet BEFORE
    const walletBeforeRes = http.get(`${ADMIN_BASE}/wallets/balance/${user_id}`, { headers });
    const walletBeforeOk = check(walletBeforeRes, { 'sellMarketOrder_wallet before → 200': (r) => r.status === 200 });
    if (!walletBeforeOk) console.error('sellMarketOrder__wallet Before:', walletBeforeRes.status, walletBeforeRes.body);

    const walletBeforeJson = safeJson(walletBeforeRes);
    const netBefore = parseFloat(walletBeforeJson?.wallets?.find(w => w.exchange === EXCHANGE)?.net_balance || 0);


    // 2. Get Instrument
    const instrumentPayload = JSON.stringify({ exchange: EXCHANGE, searchPattern: 't', page: 1, pageSize: 1 });
    const instRes = http.post(`${BASE_URL}/api/user/getInstruments`, instrumentPayload, { headers });
    const instOk = check(instRes, { 'sellMarketOrder_getInstruments → 200': (r) => r.status === 200 });
    if (!instOk) console.error('sellMarketOrder_getInstruments:', instRes.status, instRes.body);

    const instJson = safeJson(instRes);
    const instrument = instJson?.instruments?.[0];
    if (!instrument) {
      console.error('sellMarketOrder No instrument found');
      return;
    }

    const instrumentId = instrument.FinancialInstrumentID;

    // 3. Market Order
    const orderPayload = {
      instrumentId,
      exchange: EXCHANGE,
      quantity: 1,
      OrderType: 'MARKET',
      buy: false,
    };
    const orderRes = http.post(`${BASE_URL}/api/user/makeOrder`, JSON.stringify(orderPayload), { headers });
    const orderOk = check(orderRes, {
      'sellMarketOrder_makeOrder → 200': (r) => r.status === 200,
      'sellMarketOrder_makeOrder → success': (r) => r.json().status === 'success',
    });
    if (!orderOk) {
      console.error('sellMarketOrder_makeOrder Payload:', JSON.stringify(orderPayload, null, 2));
      console.error('sellMarketOrder_makeOrder Response:', orderRes.status, orderRes.body);
      return;
    }

    sleep(1);

    // 4. Wallet AFTER
    const walletAfterRes = http.get(`${ADMIN_BASE}/wallets/balance/${user_id}`, { headers });
    const walletAfterOk = check(walletAfterRes, { 'sellMarketOrder_wallet after → 200': (r) => r.status === 200 });
    if (!walletAfterOk) console.error('sellMarketOrder_Wallet After:', walletAfterRes.status, walletAfterRes.body);

    const walletAfterJson = safeJson(walletAfterRes);
    const netAfter = parseFloat(walletAfterJson?.wallets?.find(w => w.exchange === EXCHANGE)?.net_balance || 0);

    // Check portfolio to determine if this was a short cover or regular sell
    const portfolioRes = http.post(`${BASE_URL}/api/user/getPortfolio`, JSON.stringify({ exchange: EXCHANGE }), { headers });
    const portfolioJson = safeJson(portfolioRes);
    const position = portfolioJson?.portfolios?.find(p => p.FinancialInstrumentID === instrumentId);
    const wasShortCover = position && position.Quantity >= 0; // Position became positive/zero after sell

    // For sell orders:
    // - If it was a short cover (closing short position), balance may decrease due to loss
    // - If it was a regular sell (reducing long position), balance should increase
    // - Account for trading fees which may affect the balance
    const balanceDiff = netAfter - netBefore;
    const sellCheckLabel = wasShortCover
      ? 'sellMarketOrder_balance changed after short cover'
      : 'sellMarketOrder_balance increased after sell (accounting for fees)';

    const sellCheck = check({}, {
      [sellCheckLabel]: () => {
        if (wasShortCover) {
          // For short cover, just check that balance changed (could increase or decrease)
          return Math.abs(balanceDiff) > 0.01; // Allow for small rounding differences
        } else {
          // For regular sell, expect increase but allow for small fees
          return balanceDiff > -10; // Allow for reasonable trading fees
        }
      },
    });

    if (!sellCheck) {
      console.error(
        `CHECK FAILED: ${sellCheckLabel}\n` +
        `Expected: ${wasShortCover ? 'balance changed' : 'balance increased (with fee tolerance)'}\n` +
        `Actual: netBefore = ${netBefore}, netAfter = ${netAfter}, diff = ${balanceDiff}\n` +
        `Position: ${position ? `Qty=${position.Quantity}` : 'Not found'}, wasShortCover = ${wasShortCover}`
      );
    }



    // 5. Portfolio validation
    const portOk = check(portfolioRes, {
      'sellMarketOrder_getPortfolio → 200': (r) => r.status === 200,
      'sellMarketOrder_getPortfolio → includes instrument': () =>
        portfolioJson?.portfolios?.some(p => p.FinancialInstrumentID === instrumentId),
    });
    if (!portOk) {
      console.error('sellMarketOrder_Portfolio failed:', portfolioRes.status, portfolioRes.body);
    }

    sleep(1);
  });
}


// ❌ Bad Market Order Scenarios (Executed Once)
export function sellBadMarketOrder() {
  const { user_id, password, token } = getUserSafe();

  if (!user_id || !token) {
    console.error(`❌ No valid user available for VU ${__VU}`);
    return;
  }

  const headers = {
    'Content-Type': 'application/json',
    Authorization: `Bearer ${token}`,
  };

  group('❌ Market Order - Bad Scenarios', () => {
    // 1. Invalid token
    const badHeaders = { ...headers, Authorization: 'Bearer invalid-token' };
    const badPayload = JSON.stringify({ exchange: EXCHANGE, searchPattern: 't', page: 1, pageSize: 10 });

    const badAuthRes = http.post(`${BASE_URL}/api/user/getInstruments`, badPayload, { headers: badHeaders });
    const badAuthOk = check(badAuthRes, { 'getInstruments → 401 on invalid token': (r) => r.status === 401 });
    if (!badAuthOk) console.error('📥 badAuthRes:', badAuthRes.status, badAuthRes.body);

    // 2. Missing instrumentId
    const missingIdPayload = JSON.stringify({
      exchange: EXCHANGE,
      quantity: 1,
      OrderType: 'MARKET',
      buy: false,
    });

    const missingIdRes = http.post(`${BASE_URL}/api/user/makeOrder`, missingIdPayload, { headers });
    const missingIdOk = check(missingIdRes, {
      'sellMarketOrder_makeOrder → 400/422 on missing instrumentId': (r) => r.status >= 400,
    });
    if (!missingIdOk) {
      console.error('sellMarketOrder_missingId Payload:', missingIdPayload);
      console.error('sellMarketOrder_missingId Response:', missingIdRes.status, missingIdRes.body);
    }

    // 3. Invalid instrumentId
    const invalidPayload = JSON.stringify({
      instrumentId: 999999999999,
      exchange: EXCHANGE,
      quantity: 1,
      OrderType: 'MARKET',
      buy: false,
    });

    const invalidRes = http.post(`${BASE_URL}/api/user/makeOrder`, invalidPayload, { headers });
    const invalidOk = check(invalidRes, {
      'sellMarketOrder_makeOrder → fails with invalid instrumentId': (r) => r.status !== 200 || r.json().status !== 'success',
    });
    if (!invalidOk) {
      console.error('sellMarketOrder_invalidId Payload:', invalidPayload);
      console.error('sellMarketOrder_invalidId Response:', invalidRes.status, invalidRes.body);
    }

    // 4. Missing token
    const noAuthPayload = JSON.stringify({ exchange: EXCHANGE, searchPattern: 't', page: 1, pageSize: 10 });
    const noAuthHeaders = { 'Content-Type': 'application/json' };
    const noAuthRes = http.post(`${BASE_URL}/api/user/getInstruments`, noAuthPayload, { headers: noAuthHeaders });

    const noAuthOk = check(noAuthRes, {
      'sellMarketOrder_getInstruments → 403 on missing token': (r) => r.status === 403,
    });
    if (!noAuthOk) console.error('sellMarketOrder noAuthRes:', noAuthRes.status, noAuthRes.body);

    sleep(1);
  });
}

