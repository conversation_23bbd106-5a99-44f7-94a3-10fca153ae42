import http from 'k6/http';
import { group, check } from 'k6';
import {
  ADMIN_BASE,
  ADMIN_AUTH_TOKEN,
  USER_ID,         // IU-070P89
} from '../config/config.js';

const validHeaders = {
  'Content-Type': 'application/json',
  Authorization: ADMIN_AUTH_TOKEN,
};

const noAuthHeaders = {
  'Content-Type': 'application/json',
};

const badHeaders = {
  'Content-Type': 'application/json',
  Authorization: 'Bearer invalid-token',
};

export const options = {
  vus: 1,
  iterations: 1,
};

export default function () {
  goodStopLossToggle();
  badStopLossToggle();
}

// ✅ GOOD SCENARIO FLOW
export function goodStopLossToggle() {
  group('✅ stopLossToggle Good Flow', () => {
    const url = `${ADMIN_BASE}/user-leverageconfig/${USER_ID}`;

    // Step 1: Update leverage config with stop loss toggle (PUT)
    const payload = JSON.stringify({
      config: {
        NSE: { leverageChanged: false, dosquareoff: true, subagent: false },
        CRYPTO: { leverageChanged: false, dosquareoff: true, subagent: false },
        NASDAQ: { leverageChanged: false, dosquareoff: true, subagent: false },
        MCX: { leverageChanged: false, dosquareoff: true, subagent: false }
      }
    });

    const putRes = http.put(url, payload, { headers: validHeaders });

    const putOk = check(putRes, {
      'stopLossToggle PUT /user-leverageconfig → 200': (r) => r.status === 200,
      'stopLossToggle PUT → success message': (r) => {
        try {
          const json = r.json();
          return json.message?.toLowerCase().includes("success") || json.status === "success";
        } catch (e) {
          console.error('stopLossToggle PUT → JSON parse error:', e.message);
          return false;
        }
      },
    });

    if (!putOk) {
      console.error('stopLossToggle PUT leverage config failed:', putRes.status, putRes.body);
      return;
    }

    // Step 2: Confirm update via GET
    const getRes = http.get(url, { headers: validHeaders });

    const getOk = check(getRes, {
      'stopLossToggle GET /user-leverageconfig → 200': (r) => r.status === 200,
      'stopLossToggle GET → config includes exchanges': (r) => {
        try {
          const json = r.json();
          return json.config?.NSE && json.config?.MCX && json.config?.CRYPTO;
        } catch (e) {
          console.error('stopLossToggle GET → JSON parse error:', e.message);
          return false;
        }
      },
      'stopLossToggle GET → dosquareoff enabled': (r) => {
        try {
          const json = r.json();
          return json.config?.NSE?.dosquareoff === true && json.config?.MCX?.dosquareoff === true;
        } catch (e) {
          console.error('stopLossToggle GET → dosquareoff check error:', e.message);
          return false;
        }
      },
    });

    if (!getOk) {
      console.error('stopLossToggle GET leverage config failed:', getRes.status, getRes.body);
    }
  });
}

// ❌ NEGATIVE SCENARIOS
export function badStopLossToggle() {
  group('❌ stopLossToggle Bad Cases', () => {
    const badUser = 'INVALID-USER-ID';
    const badUrl = `${ADMIN_BASE}/user-leverageconfig/${badUser}`;

    // Test helper function
    const test = (desc, url, payload, headers, expectedStatus = 400) => {
      const res = http.put(url, payload, { headers });
      const ok = check(res, {
        [`${desc} → ${expectedStatus}+`]: (r) => r.status >= expectedStatus,
      });
      if (!ok) {
        console.error(`${desc} failed →`, res.status, res.body);
      }
    };

    // Test helper for GET requests
    const testGet = (desc, url, headers, expectedStatus = 400) => {
      const res = http.get(url, { headers });
      const ok = check(res, {
        [`${desc} → ${expectedStatus}+`]: (r) => r.status >= expectedStatus,
      });
      if (!ok) {
        console.error(`${desc} failed →`, res.status, res.body);
      }
    };

    // Valid payload for testing
    const validPayload = JSON.stringify({
      config: {
        NSE: { leverageChanged: false, dosquareoff: true, subagent: false },
        MCX: { leverageChanged: false, dosquareoff: true, subagent: false }
      }
    });

    // Test invalid user ID
    test(
      'stopLossToggle PUT /user-leverageconfig → invalid user_id',
      badUrl,
      validPayload,
      validHeaders
    );

    // Test malformed JSON payload
    test(
      'stopLossToggle PUT /user-leverageconfig → malformed JSON',
      `${ADMIN_BASE}/user-leverageconfig/${USER_ID}`,
      '{"config": invalid json',
      validHeaders
    );

    // Test empty payload
    test(
      'stopLossToggle PUT /user-leverageconfig → empty payload',
      `${ADMIN_BASE}/user-leverageconfig/${USER_ID}`,
      '',
      validHeaders
    );

    // Test invalid config structure
    test(
      'stopLossToggle PUT /user-leverageconfig → invalid config structure',
      `${ADMIN_BASE}/user-leverageconfig/${USER_ID}`,
      JSON.stringify({ invalidField: "test" }),
      validHeaders
    );

    // Test missing authentication
    test(
      'stopLossToggle PUT /user-leverageconfig → no auth',
      `${ADMIN_BASE}/user-leverageconfig/${USER_ID}`,
      validPayload,
      noAuthHeaders,
      401
    );

    // Test invalid authentication
    test(
      'stopLossToggle PUT /user-leverageconfig → bad token',
      `${ADMIN_BASE}/user-leverageconfig/${USER_ID}`,
      validPayload,
      badHeaders,
      401
    );

    // Test GET requests with invalid scenarios
    testGet(
      'stopLossToggle GET /user-leverageconfig → invalid user_id',
      badUrl,
      validHeaders
    );

    testGet(
      'stopLossToggle GET /user-leverageconfig → no auth',
      `${ADMIN_BASE}/user-leverageconfig/${USER_ID}`,
      noAuthHeaders,
      401
    );

    testGet(
      'stopLossToggle GET /user-leverageconfig → bad token',
      `${ADMIN_BASE}/user-leverageconfig/${USER_ID}`,
      badHeaders,
      401
    );

    // Test non-existent endpoint
    test(
      'stopLossToggle PUT /invalid-endpoint → 404',
      `${ADMIN_BASE}/invalid-endpoint/${USER_ID}`,
      validPayload,
      validHeaders,
      404
    );
  });
}
