import http from 'k6/http';
import { group, check } from 'k6';
import { ADMIN_BASE, ADMIN_AUTH_TOKEN, OVERSEE_USER_ID } from '../config/config.js';

const validHeaders = {
  'Content-Type': 'application/json',
  Authorization: ADMIN_AUTH_TOKEN,
};

const badHeaders = {
  'Content-Type': 'application/json',
  Authorization: 'Bearer invalid-token',
};

const noAuthHeaders = {
  'Content-Type': 'application/json',
};

const endpoints = {
  users: '/users/getCountsByUserStatus',
  subagents: '/users/getCountsBySubAgentStatus',
  agents: '/agents/getCountsByAgentStatus',
};

const invalidId = 'FAKE-ID';

export const options = {
  vus: 1,
  iterations: 1,
};

export default function () {
  goodUserCounts();
  badUserCounts();
}

// Legacy function names for backward compatibility
export const testUserCountsGood = goodUserCounts;
export const testUserCountsBad = badUserCounts;

// ✅ GOOD SCENARIOS
export function goodUserCounts() {
  group('✅ User Counts - Good Flow', () => {
    for (const [label, url] of Object.entries(endpoints)) {
      const fullUrl = `${ADMIN_BASE}${url}?user_id=${OVERSEE_USER_ID}`;
      const res = http.get(fullUrl, { headers: validHeaders });

      const ok = check(res, {
        [`userCounts GET ${url} → 200`]: (r) => r.status === 200,
        [`userCounts GET ${url} → has active_count`]: (r) => {
          try {
            const json = r.json();
            return typeof json.active_count === 'number';
          } catch (e) {
            console.error(`userCounts GET ${url} → JSON parse error:`, e.message);
            return false;
          }
        },
        [`userCounts GET ${url} → has inactive_count`]: (r) => {
          try {
            const json = r.json();
            return typeof json.inactive_count === 'number';
          } catch (e) {
            console.error(`userCounts GET ${url} → inactive_count check error:`, e.message);
            return false;
          }
        },
      });

      if (!ok) {
        console.error(`userCounts GET ${url} failed:`, res.status, res.body);
      }
    }
  });
}

// ❌ BAD SCENARIOS
export function badUserCounts() {
  group('❌ User Counts - Bad Scenarios', () => {
    for (const [label, url] of Object.entries(endpoints)) {
      const fullValidUrl = `${ADMIN_BASE}${url}?user_id=${OVERSEE_USER_ID}`;
      const fullInvalidUrl = `${ADMIN_BASE}${url}?user_id=${invalidId}`;

      // 1. Invalid user ID
      const invalidRes = http.get(fullInvalidUrl, { headers: validHeaders });
      const invalidOk = check(invalidRes, {
        [`userCounts GET ${url} → invalid user_id → 200`]: (r) => r.status === 200,
        [`userCounts GET ${url} → invalid user_id → count = 0`]: (r) => {
          try {
            const json = r.json();
            return json.active_count === 0 && json.inactive_count === 0;
          } catch (e) {
            console.error(`userCounts GET ${url} → invalid user_id JSON parse error:`, e.message);
            return false;
          }
        },
      });
      if (!invalidOk) {
        console.error(`userCounts GET ${url} → invalid user_id failed:`, invalidRes.status, invalidRes.body);
      }

      // 2. Missing auth
      const noAuthRes = http.get(fullValidUrl, { headers: noAuthHeaders });
      const noAuthOk = check(noAuthRes, {
        [`userCounts GET ${url} → no auth → 403+`]: (r) => r.status >= 403,
      });
      if (!noAuthOk) {
        console.error(`userCounts GET ${url} → no auth failed:`, noAuthRes.status, noAuthRes.body);
      }

      // 3. Bad token
      const badAuthRes = http.get(fullValidUrl, { headers: badHeaders });
      const badAuthOk = check(badAuthRes, {
        [`userCounts GET ${url} → bad token → 401+`]: (r) => r.status >= 401,
      });
      if (!badAuthOk) {
        console.error(`userCounts GET ${url} → bad token failed:`, badAuthRes.status, badAuthRes.body);
      }
    }
  });
}
