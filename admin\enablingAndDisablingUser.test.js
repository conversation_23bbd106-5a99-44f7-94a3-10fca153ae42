import http from 'k6/http';
import { group, check } from 'k6';
import {
  ADMIN_BASE,
  ADMIN_AUTH_TOKEN,
  USER_ID,
} from '../config/config.js';

const validHeaders = {
  'Content-Type': 'application/json',
  Authorization: ADMIN_AUTH_TOKEN,
};

const noAuthHeaders = {
  'Content-Type': 'application/json',
};

const badHeaders = {
  'Content-Type': 'application/json',
  Authorization: 'Bearer invalid-token',
};

export const options = {
  vus: 1,
  iterations: 1,
};

export default function () {
  goodUserEnableDisableFlow();
  badUserEnableDisableCases();
}

// ✅ GOOD SCENARIO FLOW
function goodUserEnableDisableFlow() {
  group('enablingAndDisablingUser Enable/Disable User Flow', () => {
    // Step 1: Check initial status
    let statusRes = http.get(`${ADMIN_BASE}/user/status/${USER_ID}`, { headers: validHeaders });

    const check1 = check(statusRes, {
      'enablingAndDisablingUser GET /user/status → 200': (r) => r.status === 200,
      'enablingAndDisablingUser GET /user/status → has is_enabled': (r) => Array.isArray(r.json().is_enabled),
    });

    if (!check1) {
      console.error('enablingAndDisablingUser Initial Status Check Failed:', statusRes.status, statusRes.body);
      return;
    }

    // Step 2: Disable user
    const disablePayload = JSON.stringify({
      user_id: USER_ID,
      is_active: 'false',
    });

    const disableRes = http.patch(`${ADMIN_BASE}/user`, disablePayload, { headers: validHeaders });

    const disableOk = check(disableRes, {
      'enablingAndDisablingUser PATCH /user disable → 200': (r) => r.status === 200,
      'enablingAndDisablingUser Disable → success message': (r) =>
        r.json().message === 'User updated successfully',
    });

    if (!disableOk) {
      console.error('enablingAndDisablingUser User Disable Failed:', disableRes.status, disableRes.body);
      return;
    }

    // Step 3: Confirm status is false
    statusRes = http.get(`${ADMIN_BASE}/user/status/${USER_ID}`, { headers: validHeaders });
    const check2 = check(statusRes, {
      'enablingAndDisablingUser Status After Disable → 200': (r) => r.status === 200,
      'enablingAndDisablingUser Status is_enabled → false': (r) => r.json().is_enabled?.[0] === false,
    });

    if (!check2) {
      console.error('enablingAndDisablingUser Post-disable status check failed:', statusRes.status, statusRes.body);
      return;
    }

    // Step 4: Re-enable user
    const enablePayload = JSON.stringify({
      user_id: USER_ID,
      is_active: 'true',
    });

    const enableRes = http.patch(`${ADMIN_BASE}/user`, enablePayload, { headers: validHeaders });

    const enableOk = check(enableRes, {
      'enablingAndDisablingUser PATCH /user enable → 200': (r) => r.status === 200,
      'enablingAndDisablingUser Enable → success message': (r) =>
        r.json().message === 'User updated successfully',
    });

    if (!enableOk) {
      console.error('enablingAndDisablingUser User Enable Failed:', enableRes.status, enableRes.body);
      return;
    }

    // Step 5: Confirm status is true
    statusRes = http.get(`${ADMIN_BASE}/user/status/${USER_ID}`, { headers: validHeaders });
    const check3 = check(statusRes, {
      'enablingAndDisablingUser Status After Enable → 200': (r) => r.status === 200,
      'enablingAndDisablingUser Status is_enabled → true': (r) => r.json().is_enabled?.[0] === true,
    });

    if (!check3) {
      console.error('enablingAndDisablingUser Post-enable status check failed:', statusRes.status, statusRes.body);
    }
  });
}

// ❌ NEGATIVE SCENARIOS
function badUserEnableDisableCases() {
  group('enablingAndDisablingUser Enable/Disable User: Bad Scenarios', () => {
    const invalidId = 'INVALID-USER';
    const badPayloads = [
      {
        desc: 'enablingAndDisablingUser PATCH /user → missing user_id',
        payload: { is_active: 'false' },
      },
      {
        desc: 'enablingAndDisablingUser PATCH /user → invalid user_id',
        payload: { user_id: invalidId, is_active: 'true' },
      },
    ];

    for (const { desc, payload } of badPayloads) {
      const res = http.patch(`${ADMIN_BASE}/user`, JSON.stringify(payload), {
        headers: validHeaders,
      });
      const ok = check(res, {
        [`${desc} → 400+`]: (r) => r.status >= 400,
      });
      if (!ok) {
        console.error(`${desc} Failed:`, res.status, res.body);
      }
    }

    // Malformed JSON
    const malformedRes = http.patch(`${ADMIN_BASE}/user`, '{"user_id": "', {
      headers: validHeaders,
    });
    const malformedOk = check(malformedRes, {
      'enablingAndDisablingUser PATCH /user → malformed JSON → 400+': (r) => r.status >= 400,
    });
    if (!malformedOk) {
      console.error('enablingAndDisablingUser Malformed JSON Failed:', malformedRes.status, malformedRes.body);
    }

    // No auth
    const noAuthRes = http.patch(`${ADMIN_BASE}/user`, JSON.stringify({
      user_id: USER_ID,
      is_active: 'true',
    }), { headers: noAuthHeaders });

    const noAuthOk = check(noAuthRes, {
      'enablingAndDisablingUser PATCH /user → no token → 403+': (r) => r.status >= 403,
    });
    if (!noAuthOk) {
      console.error('enablingAndDisablingUser No Auth Failed:', noAuthRes.status, noAuthRes.body);
    }

    // Bad token
    const badAuthRes = http.patch(`${ADMIN_BASE}/user`, JSON.stringify({
      user_id: USER_ID,
      is_active: 'true',
    }), { headers: badHeaders });

    const badAuthOk = check(badAuthRes, {
      'enablingAndDisablingUser PATCH /user → bad token → 401+': (r) => r.status >= 401,
    });
    if (!badAuthOk) {
      console.error('enablingAndDisablingUser Bad Token Failed:', badAuthRes.status, badAuthRes.body);
    }
  });
}
