{"name": "@apidevtools/json-schema-ref-parser", "version": "9.1.2", "description": "Parse, Resolve, and Dereference JSON Schema $ref pointers", "keywords": ["json", "schema", "jsonschema", "json-schema", "json-pointer", "$ref", "dereference", "resolve"], "author": {"name": "<PERSON>", "url": "https://jamesmessinger.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://apitools.dev/json-schema-ref-parser/", "repository": {"type": "git", "url": "https://github.com/APIDevTools/json-schema-ref-parser.git"}, "license": "MIT", "main": "lib/index.js", "typings": "lib/index.d.ts", "browser": {"fs": false}, "files": ["lib"], "scripts": {"build": "cp LICENSE *.md dist", "clean": "shx rm -rf .nyc_output coverage", "lint": "eslint lib test/fixtures test/specs", "test": "npm run test:node && npm run test:typescript && npm run test:browser && npm run lint", "test:node": "mocha", "test:browser": "karma start --single-run", "test:typescript": "tsc --noEmit --strict --lib esnext,dom test/specs/typescript-definition.spec.ts", "coverage": "npm run coverage:node && npm run coverage:browser", "coverage:node": "nyc node_modules/mocha/bin/mocha", "coverage:browser": "npm run test:browser -- --coverage", "upgrade": "npm-check -u && npm audit fix"}, "devDependencies": {"@babel/polyfill": "^7.12.1", "@jsdevtools/eslint-config": "^1.0.7", "@jsdevtools/host-environment": "^2.1.2", "@jsdevtools/karma-config": "^3.1.7", "@types/node": "^14.14.21", "chai": "^4.2.0", "chai-subset": "^1.6.0", "eslint": "^7.18.0", "karma": "^5.0.2", "karma-cli": "^2.0.0", "mocha": "^8.2.1", "npm-check": "^5.9.0", "nyc": "^15.0.1", "semantic-release-plugin-update-version-in-files": "^1.1.0", "shx": "^0.3.2", "typescript": "^4.0.5"}, "dependencies": {"@jsdevtools/ono": "^7.1.3", "@types/json-schema": "^7.0.6", "call-me-maybe": "^1.0.1", "js-yaml": "^4.1.0"}, "release": {"branches": ["main", "v9", {"name": "v9.1.x", "range": "9.1.x"}], "plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", ["semantic-release-plugin-update-version-in-files", {"files": ["dist/package.json"], "placeholder": "X.X.X"}], "@semantic-release/npm", "@semantic-release/github"]}}